# AI音乐播放器开发者指南

## 最新更新 (2025-01-27)

### UI优化和唱臂重新设计 (2025-01-27)
1. **评论图标美化**:
   - 重新设计评论图标为现代化气泡样式
   - 添加三个蓝色小圆点表示对话内容
   - 使用更圆润的气泡外形，提升视觉美观度
   - 修复Vector Drawable语法错误，确保编译成功

2. **歌曲信息默认显示优化**:
   - 歌曲标题默认显示："♪ 请选择歌曲 ♪"
   - 艺术家名称默认显示："享受美妙的音乐时光"
   - 增大字体尺寸：标题从28sp增加到32sp，艺术家从20sp增加到24sp
   - 向上移动位置：marginTop从8dp改为-10dp，更紧凑的布局
   - 优化空值处理逻辑，提供更友好的用户体验

3. **唱臂完全重新设计为独立全局UI元素**:
   - **独立性设计**：唱臂不再受黑胶区域限制，作为独立的UI元素完整显示
   - **尺寸优化**：唱臂高度基于整个View高度的60%，而非唱片大小
   - **位置重新定位**：位于整个View的右上角区域（55%宽度，5%高度开始）
   - **旋转中心调整**：基座位置基于唱臂图片实际结构（12%宽度，8%高度）
   - **完整显示保证**：确保唱臂在任何情况下都能完整显示，不被裁剪
   - **物理真实感**：参考真实唱片机的物理结构，提供更自然的动画效果

4. **唱臂图片透明化处理优化**:
   - **修复返回值问题**：确保`processWhiteBackgroundToTransparent`方法正确返回处理后的图片
   - **精确透明化算法**：使用更严格的白色检测（RGB>235）和接近白色检测（RGB>220且颜色差异<10）
   - **内存管理优化**：处理完成后释放原始图片内存，避免内存泄漏
   - **透明度统计**：添加透明像素百分比统计，便于调试验证
   - **绘制优化**：使用专门的Paint配置确保透明背景正确显示

5. **唱臂功能完全移除和UI优化**:
   - **完全删除唱臂相关代码**：移除所有唱臂相关的变量、方法、动画和绘制逻辑
   - **黑胶唱片位置恢复**：唱片重新居中显示，不再为唱臂预留空间
   - **搜索图标背景优化**：使用主题色背景和灰色边框，提升视觉效果
   - **搜索框交互重新设计**：初始显示140dp短搜索框，点击后延长一倍(280dp)
   - **点击外部收缩功能**：点击搜索框和按钮以外的区域自动收缩搜索框
   - **控制按钮波纹效果移除**：除播放按钮外，所有控制按钮移除波纹效果
   - **底部控制框优化**：背景更透明(alpha=0.7)，高度更矮(padding=12dp)，按钮尺寸缩小

6. **歌曲信息显示优化**:
   - **位置调整**：歌曲名和歌手名移到唱片正下方，marginTop从-10dp改为16dp
   - **尺寸优化**：歌曲标题从32sp减小到22sp，歌手名从24sp减小到16sp
   - **间距调整**：歌手名与歌曲标题间距从6dp减少到4dp
   - **默认文本简化**：将冗长的默认提示("♪ 请选择歌曲 ♪"、"享受美妙的音乐时光")简化为"暂无"
   - **视觉平衡**：文本信息紧贴唱片下方，整体布局更加紧凑协调

7. **编译验证**:
   - 修复Vector Drawable中circle元素的属性错误
   - 使用path元素替代circle元素，确保兼容性
   - 修复AlbumCoverView中discRotationSpeed未定义错误
   - 修复未使用变量警告，清理代码
   - 编译成功，无错误和警告
   - 所有UI优化均已生效，界面布局更加简洁美观

## 历史更新 (2025-05-25)

### 开发流程与协作规范更新 (2025-05-25)
- **核心流程与开发指南 (`app_flow_and_development_guidelines.md`) 中 “十、开发流程与协作规范” 章节内容进一步优化与明确**:
    - **AI角色定义强化**: 对AI (Roo) 的职责进行了更精确和严格的定义，例如增加了“规范执行与监督”、“任务理解与执行”等具体要求，并强调了代码质量和文档实时更新的责任。
    - **开发阶段要求细化**: 对“需求理解与规划”、“编码实现”、“逐步验证”、“结构化反馈”、“文档同步”、“沟通与协作”以及“任务持续性”等各个阶段中AI的责任、行为标准和交付物要求进行了更详细和严格的规定。
    - **API依赖处理强调**: 进一步明确了AI在处理`api.txt`中接口缺失或定义不明确时的暂停开发、主动报告、寻求指示的责任。
    - **规范符合性**: 在结构化反馈中增加了“规范符合性自查”的要求。
    - 此轮优化旨在为AI提供更清晰、更严格的行动指南，确保开发过程的高度规范化、任务的高质量完成，并提升与开发者的协作效率。

## 历史更新 (2024-12-19 及之前)

### 黑胶唱片和搜索功能优化 (2024-12-19)
1. **新唱臂图片集成**:
   - 使用用户提供的透明背景PNG唱臂图片 (`ic_tonearm_new.png`)
   - 优化AlbumCoverView中的唱臂尺寸和位置，适配新图片比例
   - 调整唱臂旋转角度：播放时-10°，暂停时-35°，更自然的效果
   - 优化唱臂基座位置和旋转中心点，确保动画真实感

2. **播放控制按钮UI优化**:
   - 增大所有控制按钮尺寸：歌曲列表/播放模式/评论/收藏按钮68dp，上一首/下一首72dp
   - 播放/暂停按钮80dp正圆形，确保蓝色背景为完美圆形
   - 减少按钮间距，优化padding，让图标在按钮内更大更清晰
   - 添加流畅的播放/暂停状态切换动画（旋转+缩放效果）

3. **底部控制区域优化**:
   - 背景颜色从纯黑改为更浅的灰色 (`color_gray_800`)
   - 透明度从0.8提升到0.9，提高可见性
   - 减少高度和间距，让控制区域更紧凑
   - 进度条区域间距从20dp减少到12dp

4. **搜索功能完整实现**:
   - **搜索框动态展开/收缩**：初始状态120dp短框，点击后展开到40%屏幕宽度
   - **搜索建议与搜索结果分离**：输入时显示建议，点击搜索按钮或建议后显示结果
   - **正确的点击播放逻辑**：修复MediaItem的URI设置，使用`.setUri()`而非RequestMetadata
   - **完善的交互逻辑**：点击建议执行搜索，点击结果播放歌曲并收缩搜索框
   - **键盘管理**：自动显示/隐藏键盘，焦点管理

5. **播放服务状态同步**:
   - 修复playSearchResult方法中的播放逻辑
   - 确保搜索结果播放时正确更新播放列表和当前歌曲
   - 添加详细的日志输出，便于调试播放问题
   - 优化错误处理，播放失败时显示用户友好的提示

6. **新增UI资源**:
   - `search_background_compact.xml`: 紧凑搜索框背景
   - `search_button_background.xml`: 搜索按钮圆形背景
   - `ripple_circular_button_perfect.xml`: 完美圆形播放按钮背景
   - `ic_search_white.xml`: 白色搜索图标
   - 新增尺寸资源：搜索框宽度、按钮尺寸等

### 控制按钮和收藏功能完整优化 (2024-12-19)
1. **收藏按钮状态切换优化**:
   - 创建`ic_favorite_selector.xml`状态选择器，支持选中/未选中状态
   - 选中状态显示红色实心爱心(`#F44336`)，未选中显示灰色空心爱心
   - 实现三阶段动画：放大(1.3x) → 回弹 → 心跳效果(收藏时)
   - 使用OvershootInterpolator和BounceInterpolator创建流畅动画

2. **控制按钮交互优化**:
   - 移除所有波纹效果，使用`control_button_no_ripple.xml`无波纹背景
   - 实现自定义点击动画：缩小(0.9x) → 放大(1.05x) → 恢复(1x)
   - 添加30ms触觉反馈，提升交互体验
   - 统一所有控制按钮的动画效果和时长

3. **收藏功能API完整实现**:
   - ✅ `checkLikeStatus()`: 检查歌曲收藏状态
   - ✅ `likeSong()`: 收藏歌曲API调用
   - ✅ `unlikeSong()`: 取消收藏API调用
   - ✅ 本地数据库同步：更新SongEntity收藏状态
   - ✅ 收藏歌单管理：自动添加/移除收藏歌单

4. **导航栏切换动画优化**:
   - 侧边栏显示：300ms缓入动画 + 缩放(0.95x→1x) + 透明度(0.8→1)
   - 侧边栏隐藏：300ms加速动画 + 缩放(1x→0.9x) + 透明度(1→0.5)
   - 菜单按钮淡入/淡出效果，硬件加速优化
   - Fragment切换使用自定义动画：淡入淡出 + 轻微缩放 + 平移

5. **播放/暂停按钮动画增强**:
   - 状态切换时旋转360°动画，分两阶段执行
   - 缩放效果(1x→0.8x→1x)配合旋转，视觉效果更丰富
   - 防重复动画：状态未变化时不执行动画

6. **新增动画资源**:
   - `fragment_fade_in.xml`: Fragment进入动画(淡入+缩放+平移)
   - `fragment_fade_out.xml`: Fragment退出动画(淡出+缩放+平移)
   - `control_button_no_ripple.xml`: 无波纹按钮背景
   - `ic_favorite_selector.xml`: 收藏按钮状态选择器
   - 新增收藏红色：`favorite_red`(#F44336)

### 黑胶唱片唱臂完全重构优化 (2024-12-19)
1. **学习网易云音乐唱片机设计**:
   - 参考链接：https://www.woshipm.com/rp/3974439.html
   - 理解唱片机的正确交互逻辑：播放时唱臂水平接触唱片，暂停时唱臂抬起
   - 掌握唱臂不受黑胶区域限制的布局原理

2. **唱臂图片透明背景处理**:
   - 实现`processWhiteBackgroundToTransparent()`方法，自动将白色背景转为透明
   - 支持RGB值大于240的像素自动透明化处理
   - 保持唱臂原始比例和细节，确保UI资源完全适配

3. **唱臂布局完全重构**:
   - **位置优化**：唱片居中偏左，为唱臂预留右侧完整空间
   - **尺寸适配**：唱臂高度为唱片直径的80%，保持原始宽高比
   - **旋转中心**：基座旋转轴心位于唱臂顶部偏左(10%, 15%)
   - **完整显示**：唱臂不受黑胶封面区域限制，可完整显示

4. **角度和动画优化**:
   - 播放状态：`NEEDLE_ROTATION_PLAY = 0.0f`（水平接触唱片）
   - 暂停状态：`NEEDLE_ROTATION_PAUSE = -25.0f`（抬起角度）
   - 切换动画：唱臂抬起→放下的流畅过渡，模拟真实唱片机

5. **绘制逻辑优化**:
   - 独立画布状态管理，确保唱臂绘制不受其他元素影响
   - 启用图片过滤(`isFilterBitmap = true`)提高显示质量
   - 调试模式下显示旋转中心点，便于开发调试
   - 透明背景完美融合，无白色边框问题

6. **搜索图标尺寸优化**:
   - 搜索图标从24dp增大到32dp，提升车载环境下的可见性和点击体验
   - 搜索按钮从40dp×40dp增大到52dp×52dp，更适合车载触摸操作

### 黑胶封面旋转和唱臂显示问题检查 (2024-12-19)
1. **黑胶封面旋转逻辑验证**:
   - ✅ **播放时旋转**：`startAlbumRotation()` → `binding.albumCoverView.start()`
   - ✅ **暂停时停止**：`pauseAlbumRotation()` → `binding.albumCoverView.pause()`
   - ✅ **切换歌曲时重置**：`playSongTransitionAnimation()` → `binding.albumCoverView.switchTrack()`
   - ✅ **状态观察**：PlayState.Playing/Pause/Idle 正确触发对应动画

2. **唱臂图片加载优化**:
   - 修复PNG图片加载方式：使用`BitmapFactory.decodeResource()`直接加载
   - 添加详细日志输出，便于调试唱臂图片加载问题
   - 优化透明背景处理算法，确保白色背景完全透明化
   - 增强错误处理机制，加载失败时使用默认唱臂图片

3. **唱臂布局和显示检查**:
   - 唱臂位置：唱片右上方，不受黑胶区域限制
   - 尺寸比例：唱臂高度为唱片直径的80%，保持原始宽高比
   - 旋转中心：基座位置(10%, 15%)，确保真实的旋转效果
   - 角度设置：播放时0°水平，暂停时-25°抬起

4. **调试信息增强**:
   - 添加位图加载成功/失败日志
   - 添加唱臂尺寸和位置调试信息
   - Debug模式下显示旋转中心点，便于开发调试
   - 完善异常处理和错误提示

### 编译错误和警告完全修复 (2024-12-19)
1. **Java模型类迁移完成**:
   - 创建了ApiResponse基类用于Java模型类继承，解决了编译错误
   - 将LoginStatus从Java转换为Kotlin版本，支持可空参数
   - 删除了不再使用的Java模型类：HotSearchResponse、QrCheckResponse、QrCodeResponse、QrKeyResponse、QrStatusResponse、SearchResponse、SearchSuggestResponse
   - 保留了仍在使用的Java模型类，确保功能完整性

2. **Hilt依赖注入修复**:
   - 重新创建了AppModule.kt，提供Context、SharedPreferences和AlbumArtCache的依赖注入
   - 修复了MusicApplication中的SharedPreferences注入问题，改为lazy初始化
   - 解决了所有Hilt编译错误

3. **编译警告清理**:
   - 修复了CookieInterceptor.kt中的"总是为true"条件警告
   - 修复了PlayerControllerImpl.kt中的未使用参数警告
   - 修复了RenderingOptimizer.kt中的无效空值检查警告
   - 修复了PerformanceUtils.kt中的过时API使用警告

4. **项目状态**:
   - ✅ 编译成功，无错误
   - ✅ 无编译警告
   - ✅ Hilt依赖注入正常工作
   - ✅ Java到Kotlin迁移基本完成

5. **剩余Java文件清单**:
   - **UI层**: MainActivity.java, SplashActivity.java, DrivingModeFragment.java, MusicLibraryFragment.java, SettingsFragment.java, PlaylistAdapter.java
   - **Service层**: JavaPlayerControllerWrapper.java
   - **Utils层**: LyricParser.java
   - **Model层**: 9个Java模型类（IntelligenceListResponse等）
   - **API层**: ApiResponse.java（新创建的基类）

6. **下一步优化建议**:
   - 继续将剩余Java文件迁移到Kotlin
   - 优化性能和用户体验
   - 完善单元测试
   - 添加更多功能特性

### 编译错误修复 (2024-12-19)
1. **XML文件语法错误修复**:
   - 修复automotive_app_desc.xml中的XML语法错误
   - 移除不支持的android:属性前缀，改为标准属性名
   - 修复supports-screens标签未正确关闭的问题

2. **代码编译错误修复**:
   - 修复PlayerViewModel中TAG变量初始化顺序问题
   - 修复MusicRepository中musicDataSource访问权限问题，添加getMusicDataSource()公共方法
   - 修复SearchResultsAdapter中Song.album属性访问错误，改为Song.al
   - 修复RenderingOptimizer中COLOR_MODE_WIDE_COLOR_GAMUT常量不存在问题，使用反射访问
   - 修复PlayerControllerImpl中缺少MediaMetadata导入的问题
   - 修复UnifiedPlaybackService中session.sessionToken属性错误，改为session.token

3. **API响应结构重构**:
   - 参考NeteaseCloudMusicApiBackup-main源码和api.txt文档，正确理解API响应结构
   - 创建专门的SearchResponse和SearchSuggestResponse数据类
   - 修改ApiService使用正确的响应类型而非通用BaseResponse
   - 修复MusicDataSource中的搜索方法，直接使用强类型响应数据
   - 修复SearchSuggestResult中的类型匹配问题（SuggestItem vs Song/Artist/Album）
   - 简化MusicRepository中的搜索逻辑，移除不必要的JSON解析代码

4. **数据类型一致性**:
   - 确保搜索API返回的数据结构与实际API响应一致
   - 修复类型不匹配导致的编译错误
   - 移除重复和过时的解析方法

5. **Java到Kotlin完全迁移**:
   - 删除旧的Java API文件：ApiManager.java、ApiResponse.java、ApiCallback.java、CacheInterceptor.java、CookieInterceptor.java
   - 创建Kotlin版本的CookieInterceptor，放置在network包中
   - 更新NetworkModule.kt使用新的Kotlin版本CookieInterceptor
   - 移除FlowViewModelExt.kt中对旧Java API的依赖和扩展函数
   - 确保项目完全使用Kotlin架构，不再有Java/Kotlin混合问题

6. **依赖注入错误修复**:
   - 修复FunctionalityTester中的ApiManager依赖，改为使用ApiService
   - 修复MainViewModel中的ApiManager依赖，移除不必要的ApiManager注入
   - 修复AppModule.kt中provideFunctionalityTester方法的参数类型
   - 更新所有相关的测试方法，使用新的架构
   - 解决KSP编译错误：error.NonExistentClass问题

7. **Kotlin编译错误修复**:
   - 移除AppModule.kt中残留的ApiManager导入
   - 创建Kotlin版本的ApiResponse工具类，提供getNetworkErrorMessage方法
   - 更新FlowViewModel.kt使用新的ApiResponse工具类
   - 修复RenderingOptimizer.kt中COLOR_MODE_DEFAULT常量不存在问题，使用数值0替代
   - 修复UnifiedPlaybackService.kt中SessionToken类型不匹配，使用sessionCompatToken

8. **Kotlin语法错误最终修复**:
   - 修复FunctionalityTester.kt中testApiManager方法的if表达式问题，添加明确的Unit返回类型
   - 重新创建ApiResponse.kt工具类（文件丢失问题）
   - 解决"if must have both main and else branches if used as expression"编译错误

### 任务2完成确认和修复 (2024-12-19)
1. **新歌速递自动播放完全移除**:
   - 修复PlayerViewModel中loadCachedPlaylist()方法仍调用loadNewSongs()的问题
   - 移除自动播放逻辑，改为等待用户手动选择歌曲
   - 确保首次进入播放页面不会自动播放任何歌曲

2. **播放页面UI优化**:
   - 修复底部控制区域亮度过暗问题：alpha从0.1提升到0.8
   - 确保播放按钮蓝色背景为正圆形（80dp×80dp椭圆形状）
   - 优化按钮清晰度和触摸体验

3. **搜索功能完整验证**:
   - ✅ 播放页面右上角搜索框和搜索按钮布局正确
   - ✅ 实时搜索建议功能完整实现
   - ✅ 搜索结果列表可滚动，点击可直接播放
   - ✅ 支持Enter键和搜索按钮执行搜索
   - ✅ 搜索框展开/收缩动画逻辑完整

### 播放控制按钮UI优化 (2024-12-19)
1. **控制按钮尺寸优化**:
   - 增大所有控制按钮高度：歌曲列表/播放模式/评论/收藏按钮从72dp增加到80dp
   - 增大播放相关按钮高度：上一首/下一首按钮从80dp增加到88dp
   - 增大播放/暂停按钮高度：从88dp增加到96dp，突出主要功能
   - 减少按钮内边距：从18dp减少到16dp，让图标在按钮内更大更清晰

2. **播放按钮蓝色背景优化**:
   - 修改`round_button_background.xml`，添加固定尺寸80dp×80dp
   - 确保蓝色背景更接近正圆形，长宽比例协调
   - 保持椭圆形状和蓝色渐变效果，提升视觉一致性

3. **车载横屏适配**:
   - 按钮尺寸增大后更适合车载大屏幕触摸操作
   - 保持按钮间距和布局权重，确保均匀分布
   - 优化触摸体验，减少误操作可能性

### 重大功能更新
1. **移除新歌速递自动播放**:
   - 删除首次进入播放页面的自动播放功能
   - 移除PlayerViewModel中的loadNewSongs()方法和广播接收器
   - 移除UnifiedPlaybackService中的PLAY_INITIAL_SONG处理逻辑
   - 移除MainActivity中的playInitialSong()调用

2. **新增搜索功能**:
   - 播放页面右上角搜索框和搜索按钮
   - 实时搜索建议（输入时显示）
   - 搜索结果列表（可滚动）
   - 点击搜索结果直接播放歌曲
   - 支持Enter键和搜索按钮执行搜索
   - 搜索框展开/收缩动画效果

3. **错误修复**:
   - 修复RenderingOptimizer空指针异常
   - 优化PlayerControllerImpl播放列表错误处理
   - 增强MediaItem有效性验证
   - 改进错误日志输出，避免异常抛出

4. **Android Automotive OS兼容性优化**:
   - 修复通知栏实现，符合车载MediaStyle要求
   - 优化广播接收器注册，支持Android 13+的RECEIVER_EXPORTED
   - 增强播放状态同步机制，确保UI与服务状态一致
   - 添加前台服务类型声明(mediaPlayback)
   - 优化通知渠道设置，适配车载环境

5. **全面Android Automotive系统适配**:
   - 创建车载专用主题Theme.AIMusicPlayer.Automotive
   - 添加automotive_app_desc.xml应用描述文件
   - 优化权限管理，添加车载专用权限检查
   - 增强网络安全配置，适配车载环境要求
   - 优化全屏设置和窗口管理，支持车载大屏
   - 增强RenderingOptimizer，添加车载专用渲染优化

### 搜索功能技术实现
1. **API接口**:
   - `/cloudsearch`: 云搜索API（更全面的搜索结果）
   - `/search/suggest`: 搜索建议API
   - 严格遵循api.txt文档规范

2. **数据模型**:
   - SearchResponse: 搜索响应数据模型
   - SearchSuggestResponse: 搜索建议响应模型
   - 支持歌曲、歌手、专辑的搜索建议

3. **UI组件**:
   - SearchSuggestionsAdapter: 搜索建议适配器
   - SearchResultsAdapter: 搜索结果适配器
   - 自定义搜索框背景和结果列表样式

4. **交互逻辑**:
   - 文本变化监听获取实时建议
   - 焦点变化控制搜索框展开/收缩
   - 键盘显示/隐藏管理
   - 搜索状态管理（加载、结果、错误）

### 播放状态同步机制
1. **统一状态管理**:
   - UnifiedPlaybackService: 主要播放状态源
   - PlayerControllerImpl: 状态中转和同步
   - PlayerViewModel: UI状态管理
   - 确保播放页面的歌词、封面、进度实时同步

2. **状态同步流程**:
   - ExoPlayer状态变化 → UnifiedPlaybackService监听
   - 服务状态更新 → PlayerControllerImpl同步
   - Controller状态流 → PlayerViewModel观察
   - ViewModel状态 → UI组件更新

3. **关键同步点**:
   - 播放/暂停状态同步
   - 歌曲切换时的封面和歌词更新
   - 播放进度实时同步
   - 播放列表变化同步

## 项目概述

这是一个基于Android平台的智能音乐播放器应用，采用MVVM架构模式，支持在线音乐播放、本地音乐管理、智能推荐、语音控制等功能。项目专为车载场景优化，提供横屏大屏幕适配。

## 技术栈

-   **开发语言**: Kotlin (主要) + Java (少量遗留代码)
-   **架构模式**: MVVM (Model-View-ViewModel)
-   **依赖注入**: Hilt (例如 Hilt 2.50)
-   **网络请求**: Retrofit (例如 Retrofit 2.9.0) + OkHttp (例如 OkHttp 4.12.0)
-   **图片加载**: Glide (例如 Glide 4.16.0)
-   **数据库**: Room (例如 Room 2.6.1)
-   **音频播放**: Media3 (ExoPlayer) (例如 Media3 1.2.1)
-   **UI组件**: Material Design Components + ViewBinding
-   **异步处理**: Kotlin Coroutines + Flow
-   **导航**: Navigation Component (例如 Navigation Component 2.7.5)
-   **编译工具**: KSP (替代Kapt)
-   **二维码**: ZXing (例如 ZXing 4.2.0)
-   **动画**: Lottie (例如 Lottie 6.1.0)

## 项目结构

```
app/src/main/java/com/example/aimusicplayer/
├── data/                    # 数据层
│   ├── model/              # 数据模型 (Kotlin data class)
│   │   ├── Song.kt         # 歌曲模型
│   │   ├── Album.kt        # 专辑模型
│   │   ├── Artist.kt       # 艺术家模型
│   │   ├── PlayList.kt     # 歌单模型
│   │   ├── User.kt         # 用户模型
│   │   ├── Comment.kt      # 评论模型
│   │   └── LyricLine.kt    # 歌词行模型 (原LyricEntry)
│   ├── db/                 # 数据库相关 (Room)
│   │   ├── entity/         # 数据库实体
│   │   ├── dao/            # 数据访问对象
│   │   └── AppDatabase.kt  # 数据库配置
│   ├── repository/         # 数据仓库 (继承BaseRepository)
│   │   ├── MusicRepository.kt    # 音乐数据仓库
│   │   ├── UserRepository.kt     # 用户数据仓库
│   │   ├── CommentRepository.kt  # 评论数据仓库
│   │   └── BaseRepository.kt     # 基础仓库类
│   ├── cache/              # 缓存管理 (如ApiCacheManager, AlbumArtCache)
│   └── source/             # 数据源 (如MusicDataSource, ApiService)
├── ui/                     # UI层
│   ├── adapter/            # RecyclerView适配器
│   ├── main/               # 主界面 (MainActivity)
│   ├── player/             # 播放器 (PlayerFragment, AlbumCoverView, LyricView)
│   │   ├── LyricPageFragment.kt # ViewPager2中的歌词页面Fragment
│   │   └── PlayerPagerAdapter.kt # ViewPager2适配器
│   ├── login/              # 登录 (LoginActivity, QrCodeProcessor)
│   ├── library/            # 音乐库 (MusicLibraryFragment)
│   ├── discovery/          # 音乐发现 (DiscoveryFragment)
│   ├── driving/            # 驾驶模式 (DrivingModeFragment)
│   ├── settings/           # 设置 (SettingsFragment)
│   ├── comment/            # 评论 (CommentFragment)
│   ├── intelligence/       # 心动模式 (IntelligenceFragment)
│   ├── profile/            # 用户个人资料 (UserProfileFragment)
│   ├── splash/             # 启动页 (SplashActivity)
│   └── widget/             # 自定义控件 (除播放器相关外)
├── service/                # 服务层
│   ├── UnifiedPlaybackService.kt  # 统一播放服务
│   └── PlayerController.kt / PlayerControllerImpl.kt # 播放控制接口与实现
├── utils/                  # 工具类 (如Constants, ImageUtils, LyricParser, PerformanceUtils等)
├── viewmodel/              # ViewModel层 (继承FlowViewModel)
├── di/                     # Hilt依赖注入模块 (如AppModule, NetworkModule)
└── MusicApplication.kt     # 应用入口, Hilt配置
```

## API配置

-   **基础URL**: `https://zm.armoe.cn` (请以 `Constants.kt` 或 `ApiManager.java` 中最新配置为准)
-   **接口文档**: 参考根目录 `api.txt` 文件
-   **搜索接口**:
    - `/cloudsearch`: 云搜索（type=1为歌曲搜索）
    - `/search/suggest`: 搜索建议（type=mobile）
-   **缓存机制**: 例如2分钟缓存时间 (具体看 `ApiManager` 或拦截器实现)
-   **错误处理**: 统一错误处理和重试机制
-   **Cookie管理**: 通过 `CookieInterceptor` 确保登录状态和请求的Cookie一致性。

## 核心功能模块

### 1. 用户认证模块
-   **登录方式**: 二维码登录 (已深度重构，参考ponymusic，使用ZXing本地生成，自动重试)、手机号登录 (验证码/密码)、游客登录。
-   **核心组件**: `LoginActivity.kt`, `LoginViewModel.kt`, `UserRepository.kt`, `QrCodeProcessor.kt`
-   **关键API**: `/login/qr/key`, `/login/qr/create`, `/login/qr/check`, `/captcha/sent`, `/login/cellphone`, `/register/anonimous`, `/user/account`, `/login/status`.

### 2. 音乐播放模块
-   **播放服务**: `UnifiedPlaybackService.kt` (基于Media3)。
-   **播放控制**: `PlayerFragment.kt`, `PlayerViewModel.kt`, `PlayerControllerImpl.kt`.
-   **播放界面**:
    -   **黑胶唱片**: `AlbumCoverView.kt` 实现专业级黑胶旋转动画，唱臂动画（播放/暂停/切歌时抬起落下）。
    -   **歌词显示**: `LyricView.kt` (自定义View) 实现歌词同步滚动、点击跳转、拖动更新。通过`LyricPageFragment`和`PlayerPagerAdapter`在ViewPager2中展示。
    -   **搜索功能**: 播放页面右上角搜索框，支持实时搜索建议和结果播放。
-   **功能特性**: 在线/本地音乐播放, 播放列表管理 (增删改查、清空、随机), 播放模式切换 (顺序、随机、单曲循环), 收藏/取消收藏, 评论, 智能搜索。

### 3. 音乐发现模块
-   **主要界面**: `DiscoveryFragment.kt` (具体功能视开发进度而定，可能包括排行榜、推荐歌单、新歌速递、Banner轮播图等)。
-   **核心组件**: `DiscoveryFragment.kt`, `DiscoveryViewModel.kt`, `MusicRepository.kt`.

### 4. 数据持久化
-   **Room数据库**: 用于存储如播放历史、用户偏好等本地数据。
-   **实体类**: `SongEntity.kt`, `PlaylistEntity.kt` 等。
-   **DAO接口**: 数据访问对象。
-   **缓存策略**: API响应缓存 (通过OkHttp拦截器和`ApiCacheManager`), 图片缓存 (Glide及自定义`AlbumArtCache`, `EnhancedImageCache`)。

## 开发规范

### MVVM架构规范
1.  **View层** (Activity/Fragment):
    -   只负责UI展示和用户交互。
    -   通过DataBinding/ViewBinding绑定数据。
    -   观察ViewModel的状态变化 (LiveData/Flow)。
2.  **ViewModel层**:
    -   处理业务逻辑和状态管理。
    -   继承自`FlowViewModel`基类 (如果项目中有定义)。
    -   使用Kotlin Flow或LiveData进行状态管理。
    -   不持有View的直接引用。
3.  **Model层** (Repository):
    -   负责数据访问和管理 (网络、数据库、缓存)。
    -   继承自`BaseRepository`基类 (如果项目中有定义)。
    -   为ViewModel提供数据。

### 依赖注入规范
-   全面使用Hilt进行依赖注入。
-   通过`@Module`, `@Provides`, `@Singleton`, `@Inject`, `@AndroidEntryPoint`等注解进行配置。
-   避免手动创建单例或直接实例化依赖。

### Android Automotive技术要求
1. **系统兼容性**:
   - 支持Android Automotive OS特性检测
   - 车载专用权限管理(FOREGROUND_SERVICE_MEDIA_PLAYBACK)
   - MediaSessionService正确实现
   - 通知栏MediaStyle适配

2. **UI/UX适配**:
   - 横屏大屏幕布局优化(最小宽度720dp)
   - 车载专用主题和色彩配置
   - 全屏沉浸式体验，隐藏系统栏
   - 触摸优化，支持大屏幕操作

3. **性能优化**:
   - 硬件加速渲染优化
   - 车载环境专用窗口配置
   - 宽色域支持(COLOR_MODE_WIDE_COLOR_GAMUT)
   - 屏幕常亮和电源管理

4. **安全配置**:
   - 网络安全配置适配车载环境
   - 证书固定和域名白名单
   - 调试和发布环境分离

### 代码风格
-   优先使用Kotlin，逐步替换Java代码。
-   使用Kotlin Coroutines处理异步操作。
-   遵循Android官方代码规范。
-   添加必要的注释和文档，特别是公共API和复杂逻辑。

## 构建配置
-   **目标SDK**: (例如 34, 请以 `build.gradle` 文件为准)
-   **最小SDK**: (例如 24, 请以 `build.gradle` 文件为准)
-   **编译工具**: KSP (替代Kapt用于注解处理)。
-   **`buildFeatures`**: `viewBinding true`, `buildConfig true` (确保 `BuildConfig` 文件生成)。

## 测试策略
-   **单元测试**: ViewModel和Repository层的业务逻辑。
-   **UI测试**: 关键用户流程 (Espresso / UI Automator)。
-   **集成测试**: API接口调用和数据库操作。

## 性能优化
-   **图片加载**: Glide缓存策略优化, `EnhancedImageCache` 和 `AlbumArtCache` 对特定场景优化 (如尺寸限制, RGB_565格式, 超时机制)。
-   **列表优化**: RecyclerView使用`DiffUtil`进行高效更新, ViewHolder复用。
-   **内存管理**: 及时释放资源, 避免内存泄漏 (如Handler持有Activity引用、动画监听器清理), `Bitmap`优化。
-   **网络优化**: 请求缓存 (OkHttp), 请求去重, 合理的API调用频率。
-   **启动速度**: 延迟初始化非关键组件, 后台线程预热缓存, `RenderingOptimizer`辅助优化渲染。
-   **UI渲染**: `AlbumCoverView`等自定义View的绘制优化, 硬件加速利用, `GPUPerformanceMonitor` (如有集成)。
-   **歌词解析与显示**: `EnhancedLyricParser` 优化解析效率, `LyricView` 使用二分查找等优化绘制。

## 重要更新与版本历史

### v2.5 (2025-01-25) - 编译错误和警告修复 + API现代化
-   **编译错误修复**: 修复 `ApiManager.java` 中的 `read_TIMEOUT` 变量名错误，应为 `READ_TIMEOUT`。
-   **参数名称统一**: 修复 `PlayerControllerImpl.kt` 中参数名与接口不匹配的警告，统一使用 `msec` 和 `position`。
-   **代码警告优化**:
    -   移除 `AlbumArtCache.kt` 中不必要的安全调用操作符。
    -   为 `CacheManager.kt` 中的 `GlobalScope` 使用添加 `@OptIn(DelicateCoroutinesApi::class)` 注解。
    -   修复 `DiffCallbacks.kt` 中总是为true的条件判断。
    -   移除 `ImageUtils.kt` 中未使用的变量 `rect`。
    -   为过时API添加 `@Suppress("DEPRECATION")` 注解，包括 RenderScript、NetworkInfo、Bundle.get() 等。
    -   为 unchecked cast 添加 `@Suppress("UNCHECKED_CAST")` 注解。
    -   移除冗余的 else 分支和未使用的变量。
    -   修复变量初始化问题，使用 Pair 解构赋值优化代码结构。
-   **API现代化升级**:
    -   **图像处理现代化**: 替换过时的RenderScript，使用Glide变换库和轻量级Canvas方法实现图片模糊效果。
    -   **网络检查现代化**: 更新 `NetworkUtils.kt` 和 `GlobalErrorHandler.kt`，使用现代的 `NetworkCapabilities` API，添加网络验证检查和类型识别。
    -   **振动API现代化**: 更新 `ButtonAnimationUtils.kt`，支持Android 12+的 `VibratorManager` 和现代的 `VibrationEffect` API。
    -   **UI控制现代化**: 更新 `RenderingOptimizer.kt`，使用Android 11+的 `WindowInsetsController` 和AndroidX兼容库实现沉浸式UI。
-   **编译成功**: 解决所有编译错误，项目现在可以成功编译，大幅减少过时API警告，提升代码现代化程度。

### v2.4 (2025-05-24 及之后) - UI、登录、API修复
-   **播放控制按钮布局优化**: `fragment_player.xml` 改为单行布局，播放键居中，移除画板功能。
-   **黑胶播放器界面优化**: `AlbumCoverView.kt` 调整黑胶唱片位置，确保封面完整显示，优化唱针位置。
-   **播放服务稳定性修复**: `PlayerControllerImpl.kt` 修复 `replaceAll` 方法中的空指针异常，增强错误日志。
-   **图片加载优化**: `ImageUtils.kt` 为Glide图片加载添加3秒超时机制和默认图片回退。
-   **API接口调用修复**:
    -   `ApiService.kt`: 新歌速递接口从 `/personalized/newsong` 改为 `/top/song`，添加地区类型参数。
    -   `NewSongsResponse.kt`: 优化数据结构，兼容新旧API返回格式。
    -   `Song.kt`: 添加 `getAlbumCoverUrl` 方法智能获取专辑封面URL。
    -   `MusicDataSource.kt`: 优化 `songToMediaItem` 和 `songToEntity` 方法。
-   **默认歌词显示**: `PlayerFragment.kt` 在无歌词时显示包含歌曲信息的默认歌词。
-   **二维码登录重构**: `QrCodeProcessor.kt` 和 `LoginViewModel.kt` 深度重构，参考ponymusic项目，实现本地生成二维码 (ZXing)、自动重试、优化状态检查。
-   **用户信息获取重构**: `LoginViewModel.kt` 采用双重API调用策略 (`/login/status` 和 `/user/account`)，正确解析不同响应结构。
-   **Cookie管理**: `NetworkModule.kt` 添加 `CookieInterceptor` 确保Cookie正确传递。
-   **播放控制系统优化**: `PlayerController` 和 `PlayerControllerImpl` 学习ponymusic架构，完善状态管理、播放列表、播放模式持久化。
-   **评论功能**: 确认 `CommentViewModel`, `CommentRepository`, `CommentFragment` 功能完整性。

### 唱臂PNG图片资源集成 (2025-01-25)
-   将用户提供的透明背景PNG唱臂图片 (`ic_tonearm_new.png`) 集成到 `AlbumCoverView.kt`。
-   实现切换歌曲时唱臂抬起再放下的动画效果 (`switchTrack()` 方法)。
-   使用 `AccelerateInterpolator` 和 `DecelerateInterpolator` 优化动画平滑度。

### 编译错误修复和UI优化 (2025-01-24)
-   **Vector Drawable编译错误**: 修复 `ic_tonearm.xml` 中因使用不支持的属性导致的编译错误，改用 `<path>` 实现。
-   **播放器按钮布局**: 删除其他按钮背景，仅保留播放按钮蓝色背景，增大按钮尺寸和间距。
-   **PlayerFragment重建**: 修复因文件为空导致的播放控制失效问题，重新实现核心功能。
-   **播放控制按钮顺序调整**: 调整为：歌曲列表 - 播放模式 - 上一首 - 播放/暂停 - 下一首 - 评论 - 收藏。
-   **通知栏功能优化**: 修复广播Action不匹配问题，完善 `onStartCommand` 处理逻辑。
-   **PlayerViewModel功能完善**: 新增 `playAtIndex`, `removeFromPlaylist`, `clearPlaylist`, `shufflePlaylist` 等方法。

### 播放页面卡死崩溃问题修复 (2025-01-24, 稍早)
-   **问题**: ViewPager2未设置适配器导致 `findLyricViewInViewPager()` 返回null。
-   **修复**:
    -   创建 `PlayerPagerAdapter.kt` 和 `LyricPageFragment.kt`。
    -   `PlayerFragment.kt`: 设置ViewPager2适配器，修复 `findLyricViewInViewPager()`。

### 系统性问题分析和修复 (2025-01-24)
-   **OpenGL渲染问题**: 通过 `RenderingOptimizer` 和硬件加速配置优化。
-   **PlayerFragment初始化优化**: 简化为两阶段初始化，使用协程确保线程安全。
-   **MainActivity启动流程优化**: 移除Handler嵌套，使用 `View.post`。
-   **控制器图标优化**: 重新设计三条横线菜单图标。
-   **侧边栏动画性能优化**: 启用硬件加速，优化动画曲线。
-   **图片加载性能优化**: `EnhancedImageCache` 减少图片尺寸、使用RGB_565、智能降采样；`BlurUtils` 优化。
-   **导航逻辑优化**: Fragment切换动画替换为轻量级淡入淡出。

### v2.3 及更早版本重要修复 (2024-12-19 及之前)

-   **全面代码清理与重构 (多次)**:
    -   删除了大量Java和Kotlin的重复模型类、工具类、适配器等。
    -   统一数据模型到 `data/model/` 目录下的Kotlin版本。
    -   统一API服务接口使用 `ApiService.kt`。
    -   清理了旧架构的 `adapter/` 目录，适配器统一到 `ui/adapter/`。
    -   修复了大量因重复文件和引用错误导致的编译问题。
-   **Hilt依赖注入错误修复**: 解决重复绑定、缺少绑定等问题。
-   **KSP编译错误修复**: 解决 `error.NonExistentClass` (如 `AlbumArtCache` 注入问题)，`AndroidManifest.xml` 权限问题。
-   **`BuildConfig` 引用修复**: 在 `app/build.gradle` 中添加 `buildConfig true` 解决AGP 8.x问题。
-   **空指针和闪退修复**:
    -   `AlbumCoverView.kt`: `needleBitmap` 空指针 (Vector Drawable解码问题)。
    -   `SplashActivity.java`: `navigationAction.ordinal()` 空指针。
    -   `PlayerControllerImpl.kt`: `random.nextInt(playlist.size)` 播放列表为空时异常。
-   **UI与UX优化**:
    -   黑胶播放器界面重构 (`AlbumCoverView`, `ic_playing_needle.xml`, `bg_playing_disc.xml`)，参考云音乐设计，确保封面嵌入和唱臂真实感。
    -   修复Vector Drawable因 `fillColor=\"none\"` 导致的编译错误。
    -   播放控制按钮布局调整。
    -   歌词显示优化 (统一使用 `LyricView`, 默认歌词提示)。
-   **API URL更新**: `BASE_URL` 更新为 `https://zm.armoe.cn`。
-   **登录功能修复**:
    -   修复API响应JSON解析错误 (`Expected a string but was BEGIN_OBJECT`)，`ApiService` 返回类型改为 `ResponseBody`。
    -   `UserRepository` 添加JSON响应解析。
    -   修复了 `LoginActivity` 中 `isActive` 在 `lifecycleScope` 中不可用的问题。
-   **导航配置修复**: `nav_graph.xml` 的 `startDestination` 指向存在的Fragment，修复应用崩溃。
-   **应用卡死问题**: 优化 `UnifiedPlaybackService` 初始化 (异步ExoPlayer)，简化 `MainActivity` 初始化 (移除嵌套Handler)。
-   **侧边栏菜单功能实现**: 箭头改三条横线，点击展开/收起，暗淡覆盖层，点击外部关闭。
-   **歌词、播放列表、收藏功能增强**: 包括交互、动画、错误处理。
-   **性能优化**: 启动速度、内存使用、缓存系统（预加载、清理）。

## 已知问题和改进计划

-   **功能完善**:
    -   驾驶模式的具体功能实现。
    -   音乐库的本地音乐扫描和管理功能细化。
    -   设置页面更多个性化选项。
-   **性能持续优化**:
    -   进一步优化列表滚动性能，尤其是在低端设备上。
    -   监控并减少潜在的内存抖动和泄漏。
-   **用户体验提升**:
    -   增加更多平滑的过渡动画和微交互。
    -   完善错误提示和用户引导。
-   **代码质量**:
    -   继续将遗留的Java代码（如部分Fragment和工具类）迁移到Kotlin。
    -   增加单元测试和UI测试的覆盖率。
-   **车载场景适配**:
    -   针对不同车载系统和屏幕尺寸进行更细致的兼容性测试和优化。
    -   语音控制功能的进一步增强和鲁棒性提升。

## 贡献指南
1.  遵循MVVM架构模式。
2.  优先使用Kotlin进行新功能开发和代码重构。
3.  为新功能和重要修复添加适当的单元测试或UI测试。
4.  及时更新本文档和相关代码注释。
5.  确保代码风格统一，并通过CI检查（如果配置）。
6.  提交Pull Request前，请确保代码已在本地成功编译并运行通过核心功能测试。
