<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_player" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/fragment_player_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="394" endOffset="16"/></Target><Target id="@+id/background_blur" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="29"/></Target><Target id="@+id/background_overlay" view="View"><Expressions/><location startLine="17" startOffset="4" endLine="22" endOffset="29"/></Target><Target id="@+id/content_container" view="LinearLayout"><Expressions/><location startLine="25" startOffset="4" endLine="217" endOffset="18"/></Target><Target id="@+id/album_cover_view" view="com.example.aimusicplayer.ui.widget.AlbumCoverView"><Expressions/><location startLine="41" startOffset="12" endLine="46" endOffset="49"/></Target><Target id="@+id/album_art" view="ImageView"><Expressions/><location startLine="49" startOffset="12" endLine="60" endOffset="57"/></Target><Target id="@+id/vinyl_background" view="ImageView"><Expressions/><location startLine="63" startOffset="12" endLine="71" endOffset="43"/></Target><Target id="@+id/song_title" view="TextView"><Expressions/><location startLine="83" startOffset="16" endLine="98" endOffset="46"/></Target><Target id="@+id/song_artist" view="TextView"><Expressions/><location startLine="100" startOffset="16" endLine="115" endOffset="46"/></Target><Target id="@+id/search_container" view="RelativeLayout"><Expressions/><location startLine="128" startOffset="12" endLine="169" endOffset="28"/></Target><Target id="@+id/search_edit_text" view="EditText"><Expressions/><location startLine="137" startOffset="16" endLine="155" endOffset="47"/></Target><Target id="@+id/search_button" view="ImageView"><Expressions/><location startLine="158" startOffset="16" endLine="168" endOffset="46"/></Target><Target id="@+id/search_suggestions_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="172" startOffset="12" endLine="181" endOffset="56"/></Target><Target id="@+id/search_results_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="184" startOffset="12" endLine="193" endOffset="41"/></Target><Target id="@+id/tab_layout_player" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="196" startOffset="12" endLine="206" endOffset="43"/></Target><Target id="@+id/view_pager_player" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="209" startOffset="12" endLine="215" endOffset="54"/></Target><Target id="@+id/loading_view" view="com.example.aimusicplayer.ui.widget.LottieLoadingView"><Expressions/><location startLine="220" startOffset="4" endLine="230" endOffset="25"/></Target><Target id="@+id/control_container" view="LinearLayout"><Expressions/><location startLine="233" startOffset="4" endLine="393" endOffset="18"/></Target><Target id="@+id/textview_player_current_time" view="TextView"><Expressions/><location startLine="253" startOffset="12" endLine="261" endOffset="38"/></Target><Target id="@+id/seekbar_player_progress" view="SeekBar"><Expressions/><location startLine="263" startOffset="12" endLine="273" endOffset="43"/></Target><Target id="@+id/textview_player_total_time" view="TextView"><Expressions/><location startLine="275" startOffset="12" endLine="283" endOffset="38"/></Target><Target id="@+id/button_player_playlist" view="ImageView"><Expressions/><location startLine="296" startOffset="12" endLine="307" endOffset="73"/></Target><Target id="@+id/button_player_play_mode" view="ImageView"><Expressions/><location startLine="310" startOffset="12" endLine="321" endOffset="73"/></Target><Target id="@+id/button_player_prev" view="ImageView"><Expressions/><location startLine="324" startOffset="12" endLine="335" endOffset="73"/></Target><Target id="@+id/button_player_play_pause" view="ImageView"><Expressions/><location startLine="338" startOffset="12" endLine="349" endOffset="79"/></Target><Target id="@+id/button_player_next" view="ImageView"><Expressions/><location startLine="352" startOffset="12" endLine="363" endOffset="73"/></Target><Target id="@+id/button_player_comment" view="ImageView"><Expressions/><location startLine="366" startOffset="12" endLine="377" endOffset="73"/></Target><Target id="@+id/button_player_collect" view="ImageView"><Expressions/><location startLine="380" startOffset="12" endLine="391" endOffset="73"/></Target></Targets></Layout>