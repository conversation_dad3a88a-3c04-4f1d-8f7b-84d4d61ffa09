package com.example.aimusicplayer.ui.player

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable // 导入 Drawable
// import androidx.graphics.drawable.Drawable // Removed duplicate import
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.fragment.app.Fragment
import android.view.animation.LinearInterpolator
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.palette.graphics.Palette
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.example.aimusicplayer.R
import com.example.aimusicplayer.ui.adapter.CommentAdapter
import com.example.aimusicplayer.ui.adapter.MediaItemAdapter
import com.example.aimusicplayer.ui.adapter.SearchResultsAdapter
import com.example.aimusicplayer.ui.adapter.SearchSuggestionsAdapter
import com.example.aimusicplayer.databinding.FragmentPlayerBinding
// 删除不再需要的LyricAdapter和EmptyLyricAdapter导入
import com.example.aimusicplayer.service.PlayMode
import com.example.aimusicplayer.service.PlayState
import com.example.aimusicplayer.utils.AlbumArtCache
import com.example.aimusicplayer.utils.AnimationUtils
import com.example.aimusicplayer.utils.EnhancedImageCache
import com.example.aimusicplayer.utils.EnhancedLyricParser
import com.example.aimusicplayer.utils.ImageUtils
import com.example.aimusicplayer.utils.TimeUtils
import com.example.aimusicplayer.data.model.LyricInfo
import com.example.aimusicplayer.data.model.LyricLine
import com.example.aimusicplayer.ui.player.LyricView
import com.example.aimusicplayer.viewmodel.PlayerViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.collectLatest
import java.util.regex.Pattern
import javax.inject.Inject
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager

/**
 * 播放器Fragment
 * 使用MVVM架构
 */
@AndroidEntryPoint
class PlayerFragment : Fragment() {

    private var _binding: FragmentPlayerBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PlayerViewModel by viewModels()

    @Inject
    lateinit var albumArtCache: AlbumArtCache

    // 增强型图片缓存
    private lateinit var enhancedImageCache: EnhancedImageCache

    // 状态变量
    private var isSeekBarDragging = false
    private var isLyricMode = false
    private var backgroundColorAnimator: android.animation.ValueAnimator? = null

    // 专辑旋转动画
    private var albumRotationAnimator: ObjectAnimator? = null
    private var currentRotation = 0f

    // 歌曲切换动画
    private var songTransitionAnimator: ValueAnimator? = null
    private var previousSongId: Long = -1

    // 歌词拖动相关变量
    private var lyricDragging = false
    private var lyricDragStartY = 0f
    private var lyricDragStartTime = 0L

    // 搜索相关变量
    private lateinit var searchSuggestionsAdapter: SearchSuggestionsAdapter
    private lateinit var searchResultsAdapter: SearchResultsAdapter
    private var isSearchExpanded = false

    // 动画相关常量
    companion object {
        private const val BACKGROUND_ANIMATION_DURATION = 800L
        private const val COVER_ANIMATION_DURATION = 500L
        private const val SONG_TRANSITION_DURATION = 600L
        private const val ALBUM_ROTATION_DURATION = 20000L // 20秒旋转一圈
        private const val TAG = "PlayerFragment"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPlayerBinding.inflate(inflater, container, false)

        // 初始化增强型图片缓存
        enhancedImageCache = EnhancedImageCache.getInstance(requireContext())

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 简化为两阶段初始化，提高稳定性
        initializeUIComponents()
    }

    /**
     * 第一阶段：UI组件初始化（同步）
     */
    private fun initializeUIComponents() {
        Log.d(TAG, "开始UI组件初始化")

        try {
            // 基础UI设置
            setupBasicUI()

            // ViewPager2设置
            setupViewPager()

            // 复杂交互设置
            setupComplexInteractions()

            // 异步初始化服务连接和观察者
            lifecycleScope.launch {
                delay(50) // 短暂延迟确保UI渲染完成
                initializeServicesAndObservers()
            }
        } catch (e: Exception) {
            Log.e(TAG, "UI组件初始化失败", e)
            // 降级处理：仅设置基础UI
            try {
                setupBasicUI()
                hideSidebarIfNeeded()
            } catch (fallbackError: Exception) {
                Log.e(TAG, "降级初始化也失败", fallbackError)
            }
        }
    }

    /**
     * 第二阶段：服务连接和观察者（异步）
     */
    private suspend fun initializeServicesAndObservers() {
        Log.d(TAG, "开始服务连接和观察者初始化")

        try {
            // 在主线程设置观察者
            withContext(Dispatchers.Main) {
                // 检查Fragment是否仍然有效且View已创建
                if (!isAdded || view == null || _binding == null) {
                    Log.w(TAG, "Fragment已销毁或View为null，跳过观察者设置")
                    return@withContext
                }

                // 确保ViewLifecycleOwner可用
                try {
                    val lifecycleOwner = viewLifecycleOwner
                    if (lifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.CREATED)) {
                        setupObservers()
                        hideSidebarIfNeeded()
                        Log.d(TAG, "观察者设置成功")
                    } else {
                        Log.w(TAG, "ViewLifecycleOwner状态不正确，跳过观察者设置")
                    }
                } catch (e: IllegalStateException) {
                    Log.w(TAG, "ViewLifecycleOwner不可用，延迟设置观察者", e)
                    // 延迟重试
                    delay(100)
                    if (isAdded && view != null && _binding != null) {
                        try {
                            setupObservers()
                            hideSidebarIfNeeded()
                            Log.d(TAG, "延迟观察者设置成功")
                        } catch (retryException: Exception) {
                            Log.e(TAG, "延迟观察者设置也失败", retryException)
                        }
                    }
                }
            }

            Log.d(TAG, "PlayerFragment初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "服务连接和观察者初始化失败", e)
        }
    }

    /**
     * 设置基础UI组件（第一阶段）
     */
    private fun setupBasicUI() {
        // 设置播放/暂停按钮点击事件
        binding.buttonPlayerPlayPause.setOnClickListener {
            addButtonClickEffect(it)
            viewModel.togglePlayPause()
        }

        // 设置上一首按钮点击事件
        binding.buttonPlayerPrev.setOnClickListener {
            addButtonClickEffect(it)
            viewModel.skipToPrevious()
        }

        // 设置下一首按钮点击事件
        binding.buttonPlayerNext.setOnClickListener {
            addButtonClickEffect(it)
            viewModel.skipToNext()
        }

        // 设置播放模式按钮点击事件
        binding.buttonPlayerPlayMode.setOnClickListener {
            addButtonClickEffect(it)
            viewModel.togglePlayMode()
        }

        // 设置收藏按钮点击事件
        binding.buttonPlayerCollect.setOnClickListener {
            addButtonClickEffect(it)
            toggleCollect()
        }



        // 设置播放列表按钮点击事件
        binding.buttonPlayerPlaylist.setOnClickListener {
            addButtonClickEffect(it)
            showPlaylistDialog()
        }

        // 基础进度条设置
        binding.seekbarPlayerProgress.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    binding.textviewPlayerCurrentTime.text = TimeUtils.formatTime(progress.toLong())
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = true
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = false
                seekBar?.progress?.let { progress ->
                    viewModel.seekTo(progress)
                }
            }
        })

        // 初始化搜索功能
        setupSearchFunction()

        Log.d(TAG, "基础UI设置完成")
    }

    /**
     * 设置复杂交互组件（在第二阶段调用）
     */
    private fun setupComplexInteractions() {
        try {
            // 专辑封面和歌词切换
            binding.albumArt.setOnClickListener {
                toggleLyricMode()
            }

            binding.viewPagerPlayer.setOnClickListener {
                toggleLyricMode()
            }

            // 设置歌词点击跳转
            setupLyricInteraction()

            Log.d(TAG, "复杂交互设置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置复杂交互失败", e)
        }
    }

    /**
     * 设置ViewPager2适配器
     */
    private fun setupViewPager() {
        try {
            // 创建简单的适配器，只显示歌词页面
            val adapter = PlayerPagerAdapter(this)
            binding.viewPagerPlayer.adapter = adapter

            // 设置默认显示歌词页面
            binding.viewPagerPlayer.currentItem = 0

            Log.d(TAG, "ViewPager2适配器设置成功")
        } catch (e: Exception) {
            Log.e(TAG, "设置ViewPager2适配器失败", e)
        }
    }

    /**
     * 隐藏侧边栏（如果需要）
     */
    private fun hideSidebarIfNeeded() {
        try {
            // 获取MainActivity的SidebarController
            val activity = requireActivity()
            if (activity is com.example.aimusicplayer.ui.main.MainActivity) {
                // 通过反射或其他方式获取SidebarController并隐藏侧边栏
                // 这里暂时使用简单的方法
                Log.d(TAG, "确保侧边栏隐藏")
            }
        } catch (e: Exception) {
            Log.e(TAG, "隐藏侧边栏失败", e)
        }
    }

    /**
     * 添加按钮点击效果 - 使用优化的动画工具
     */
    private fun addButtonClickEffect(view: View) {
        // 使用新的动画工具提供按钮反馈
        AnimationUtils.buttonClickFeedback(view, 0.9f, 120)

        // 添加触觉反馈
        addHapticFeedback()
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察当前歌曲
        viewModel.currentSong.observe(viewLifecycleOwner) { mediaItem ->
            mediaItem?.let {
                // 获取歌曲ID
                val songId = it.mediaId.toLongOrNull() ?: 0L

                // 检查是否是新歌曲
                val isSongChanged = previousSongId != songId

                // 如果是新歌曲，添加切换动画
                if (isSongChanged && previousSongId != -1L) {
                    playSongTransitionAnimation()
                }

                // 更新歌曲信息 - 优化默认显示
                val title = it.mediaMetadata.title?.toString()
                val artist = it.mediaMetadata.artist?.toString()

                binding.songTitle.text = if (title.isNullOrBlank()) {
                    "♪ 请选择歌曲 ♪"
                } else {
                    title
                }

                binding.songArtist.text = if (artist.isNullOrBlank()) {
                    "享受美妙的音乐时光"
                } else {
                    artist
                }

                // 加载专辑封面 - 优化版本，调用API获取高质量封面
                lifecycleScope.launch {
                    try {
                        // 先获取歌曲详情以获取高质量专辑封面
                        val songDetail = viewModel.getSongDetail(songId)
                        val artworkUri = songDetail?.al?.picUrl?.let { Uri.parse(it) }
                            ?: it.mediaMetadata.artworkUri

                        if (artworkUri != null) {
                            // 生成缓存键
                            val cacheKey = "album_${songId}"

                            // 从缓存获取
                            val bitmap = enhancedImageCache.getBitmap(artworkUri, cacheKey)

                            if (bitmap != null) {
                                // 使用缓存的专辑封面
                                updateAlbumArt(bitmap, isSongChanged)
                            } else {
                                // 如果缓存中没有，使用默认封面
                                try {
                                    val defaultCover = BitmapFactory.decodeResource(resources, R.drawable.default_album_art)
                                    if (defaultCover != null) {
                                        updateAlbumArt(defaultCover, isSongChanged)
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "加载默认封面失败", e)
                                }

                                // 异步加载并缓存高质量封面
                                loadAlbumArtWithEnhancedCache(artworkUri, songId)
                            }
                        } else {
                            // 使用默认封面
                            try {
                                val defaultCover = BitmapFactory.decodeResource(resources, R.drawable.default_album_art)
                                if (defaultCover != null) {
                                    updateAlbumArt(defaultCover, isSongChanged)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "加载默认封面失败", e)
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "加载专辑封面失败", e)
                        // 加载失败，使用默认封面
                        try {
                            val defaultCover = BitmapFactory.decodeResource(resources, R.drawable.default_album_art)
                            if (defaultCover != null) {
                                updateAlbumArt(defaultCover, isSongChanged)
                            }
                        } catch (ex: Exception) {
                            Log.e(TAG, "加载默认封面失败", ex)
                        }
                    }
                }

                // 加载歌词
                viewModel.loadLyricInfo(songId)

                // 更新收藏状态
                updateCollectButton(viewModel.isCurrentSongCollected.value ?: false)

                // 保存当前歌曲ID
                previousSongId = songId
            }
        }

        // 观察播放状态
        viewModel.playState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is PlayState.Playing -> {
                    updatePlayPauseButton(true)
                    startAlbumRotation()
                }
                is PlayState.Pause -> {
                    updatePlayPauseButton(false)
                    pauseAlbumRotation()
                }
                is PlayState.Idle -> {
                    updatePlayPauseButton(false)
                    resetAlbumRotation()
                }
                else -> {
                    // 其他状态
                }
            }
        }

        // 观察播放进度
        viewModel.playProgress.observe(viewLifecycleOwner) { currentPositionMs ->
            if (!isSeekBarDragging) {
                binding.seekbarPlayerProgress.progress = currentPositionMs.toInt()
                binding.textviewPlayerCurrentTime.text = TimeUtils.formatTime(currentPositionMs)
                val totalDuration = viewModel.duration.value ?: 0L
                binding.textviewPlayerTotalTime.text = TimeUtils.formatTime(totalDuration)
                binding.seekbarPlayerProgress.max = totalDuration.toInt()

                // 更新歌词位置
                updateLyricPosition(currentPositionMs)
            }
        }

        // 观察播放模式
        viewModel.playMode.observe(viewLifecycleOwner) { mode ->
            updatePlayModeButton(mode)
        }

        // 观察歌词 - 增强版本，支持实时同步和滚动
        viewModel.lyrics.observe(viewLifecycleOwner) { lyricInfo ->
            if (lyricInfo != null && !lyricInfo.isEmpty()) {
                // 解析歌词为LyricLine列表
                val lyricLines = parseLyricInfoToLines(lyricInfo)

                // 统一使用LyricView，删除适配器方式以提高效率
                // LyricView在ViewPager2的页面中，需要通过ViewPager2获取
                val lyricView = findLyricViewInViewPager()
                if (lyricView != null) {
                    // 设置歌词到LyricView
                    lyricView.setLyrics(lyricLines)

                    // 设置歌词点击监听器
                    lyricView.onLyricClickListener = { lyricLine ->
                        // 跳转到对应时间
                        viewModel.seekTo(lyricLine.time.toInt())
                    }

                    // 设置拖动监听器
                    lyricView.onDragPositionChangeListener = { lyricLine ->
                        // 实时更新播放位置
                        viewModel.seekTo(lyricLine.time.toInt())
                    }

                    // 更新当前播放位置对应的歌词
                    updateLyricPosition(viewModel.currentPosition.value ?: 0)
                } else {
                    Log.w(TAG, "LyricView未找到，无法显示歌词")
                }
            } else {
                // 没有歌词时显示默认歌词
                val lyricView = findLyricViewInViewPager()
                if (lyricView != null) {
                    val defaultLyrics = createDefaultLyrics()
                    lyricView.setLyrics(defaultLyrics)
                } else {
                    Log.w(TAG, "LyricView未找到，无法显示默认歌词")
                }
            }
        }

        // 观察当前歌曲是否已收藏
        viewModel.isCurrentSongCollected.observe(viewLifecycleOwner) { isCollected ->
            updateCollectButton(isCollected)
        }

        // 观察错误弹窗
        viewModel.showErrorDialog.observe(viewLifecycleOwner) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                showErrorDialog("错误", errorMessage)
            }
        }

        // 观察搜索建议
        lifecycleScope.launch {
            viewModel.searchSuggestions.collectLatest { suggestions ->
                searchSuggestionsAdapter.submitList(suggestions)
                binding.searchSuggestionsRecycler.visibility = if (suggestions.isNotEmpty()) View.VISIBLE else View.GONE
            }
        }

        // 观察搜索结果
        lifecycleScope.launch {
            viewModel.searchResults.collectLatest { results ->
                searchResultsAdapter.submitList(results)
                binding.searchResultsRecycler.visibility = if (results.isNotEmpty()) View.VISIBLE else View.GONE

                // 如果有搜索结果，隐藏ViewPager
                if (results.isNotEmpty()) {
                    binding.viewPagerPlayer.visibility = View.GONE
                } else {
                    binding.viewPagerPlayer.visibility = View.VISIBLE
                }
            }
        }

        // 观察搜索状态
        lifecycleScope.launch {
            viewModel.isSearching.collectLatest { isSearching ->
                // 可以在这里显示搜索加载状态
                Log.d(TAG, "搜索状态: $isSearching")
            }
        }
    }

    /**
     * 更新专辑封面 - 优化版本，增强错误处理
     * @param bitmap 专辑封面位图
     * @param withAnimation 是否添加动画效果
     */
    private fun updateAlbumArt(bitmap: Bitmap?, withAnimation: Boolean = false) {
        try {
            // 检查bitmap是否有效
            if (bitmap == null || bitmap.isRecycled) {
                Log.w(TAG, "位图无效，使用默认封面")
                val defaultBitmap = BitmapFactory.decodeResource(resources, R.drawable.default_album_art)
                if (defaultBitmap != null) {
                    updateAlbumArt(defaultBitmap, withAnimation)
                }
                return
            }

            // 设置到AlbumCoverView
            binding.albumCoverView.setCoverBitmap(bitmap)

            // 同时设置到备用ImageView（如果需要）
            if (withAnimation) {
                // 优化动画，减少重绘
                binding.albumArt.animate()
                    .alpha(0f)
                    .scaleX(0.9f)
                    .scaleY(0.9f)
                    .setDuration(COVER_ANIMATION_DURATION / 2)
                    .withEndAction {
                        try {
                            // 设置新封面
                            binding.albumArt.setImageBitmap(bitmap)

                            // 淡入新封面
                            binding.albumArt.animate()
                                .alpha(1f)
                                .scaleX(1f)
                                .scaleY(1f)
                                .setDuration(COVER_ANIMATION_DURATION / 2)
                                .start()
                        } catch (e: Exception) {
                            Log.e(TAG, "动画设置封面失败", e)
                        }
                    }
                    .start()
            } else {
                // 直接设置专辑封面
                binding.albumArt.setImageBitmap(bitmap)
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新专辑封面失败", e)
            // 显示用户友好的错误提示
            showErrorSnackbar("专辑封面加载失败")
        }

        // 检查bitmap是否为null
        bitmap?.let { validBitmap ->
            // 生成调色板并提取颜色
            Palette.from(validBitmap).generate { palette ->
                palette?.let {
                    // 获取主色调
                    val dominantColor = it.getDominantColor(0xFF333333.toInt())

                    // 获取鲜艳的颜色
                    val vibrantColor = it.getVibrantColor(dominantColor)

                    // 使用鲜艳的颜色或主色调
                    val finalColor = if (vibrantColor != dominantColor) vibrantColor else dominantColor

                    // 更新背景颜色
                    updateBackgroundColor(finalColor)

                    // 获取文本颜色
                    val textColor = if (ColorUtils.calculateLuminance(finalColor) > 0.5) {
                        Color.BLACK
                    } else {
                        Color.WHITE
                    }

                    // 更新文本颜色
                    updateTextColor(textColor)
                }
            }

            // 生成模糊背景
            lifecycleScope.launch {
                val blurredBitmap = withContext(Dispatchers.IO) {
                    ImageUtils.blurBitmap(requireContext(), validBitmap, 25)
                }
                updateBlurredBackground(blurredBitmap, withAnimation)
            }
        }
    }

    /**
     * 使用增强型缓存加载专辑封面
     */
    private fun loadAlbumArtWithEnhancedCache(artworkUri: android.net.Uri, songId: Long) {
        // 生成缓存键
        val cacheKey = "album_${songId}"

        // 加载专辑封面到ImageView
        Glide.with(this)
            .asBitmap()
            .load(artworkUri)
            .apply(RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .centerCrop())
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    // 设置专辑封面
                    binding.albumArt.setImageBitmap(resource)

                    // 提取颜色
                    extractColorFromBitmap(resource)

                    // 生成模糊背景
                    lifecycleScope.launch {
                        val blurredBitmap = withContext(Dispatchers.IO) {
                            ImageUtils.blurBitmap(requireContext(), resource, 25)
                        }
                        updateBlurredBackground(blurredBitmap)

                        // 缓存模糊背景
                        enhancedImageCache.saveBitmap(blurredBitmap, "blur_${cacheKey}")
                    }

                    // 缓存专辑封面
                    lifecycleScope.launch(Dispatchers.IO) {
                        enhancedImageCache.saveBitmap(resource, cacheKey)
                    }
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    Log.e(TAG, "加载专辑封面失败")
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // 不需要处理
                }
            })
    }

    /**
     * 从位图中提取颜色
     */
    private fun extractColorFromBitmap(bitmap: Bitmap) {
        lifecycleScope.launch(Dispatchers.Default) {
            // 使用Palette提取颜色
            val palette = Palette.from(bitmap).generate()

            // 获取主色调
            val dominantColor = palette.getDominantColor(0xFF333333.toInt())

            // 获取鲜艳的颜色
            val vibrantColor = palette.getVibrantColor(dominantColor)

            // 使用鲜艳的颜色或主色调
            val finalColor = if (vibrantColor != dominantColor) vibrantColor else dominantColor

            // 在主线程更新UI
            withContext(Dispatchers.Main) {
                // 更新背景颜色
                updateBackgroundColor(finalColor)

                // 获取文本颜色
                val textColor = if (ColorUtils.calculateLuminance(finalColor) > 0.5) {
                    Color.BLACK
                } else {
                    Color.WHITE
                }

                // 更新文本颜色
                updateTextColor(textColor)
            }
        }
    }

    /**
     * 设置歌词点击跳转
     */
    /**
     * 设置歌词交互 - 优化版本，统一使用LyricView
     */
    private fun setupLyricInteraction() {
        // 设置歌词点击事件 - 直接切换歌词/封面显示模式
        binding.viewPagerPlayer.setOnClickListener {
            toggleLyricMode()
        }

        // 歌词交互已经在LyricView中实现，这里不需要额外的触摸处理
        // LyricView自带点击跳转和拖动交互功能
    }

    /**
     * 创建默认歌词
     */
    private fun createDefaultLyrics(): List<LyricLine> {
        val currentSong = viewModel.currentSong.value
        val songTitle = currentSong?.mediaMetadata?.title?.toString() ?: "未知歌曲"
        val artist = currentSong?.mediaMetadata?.artist?.toString() ?: "未知艺术家"

        // 根据歌曲类型提供不同的提示文字
        val noLyricText = when {
            songTitle.contains("纯音乐", ignoreCase = true) ||
            songTitle.contains("轻音乐", ignoreCase = true) ||
            songTitle.contains("instrumental", ignoreCase = true) ||
            artist.contains("轻音乐", ignoreCase = true) ||
            artist.contains("纯音乐", ignoreCase = true) -> "♪ 纯音乐，请静心聆听 ♪"
            songTitle.contains("钢琴", ignoreCase = true) ||
            artist.contains("钢琴", ignoreCase = true) -> "♪ 钢琴曲，感受指尖的优雅 ♪"
            songTitle.contains("古典", ignoreCase = true) ||
            artist.contains("古典", ignoreCase = true) -> "♪ 古典音乐，品味永恒之美 ♪"
            songTitle.contains("爵士", ignoreCase = true) ||
            artist.contains("爵士", ignoreCase = true) -> "♫ 爵士乐，享受自由的节拍 ♫"
            else -> "暂无歌词"
        }

        return listOf(
            LyricLine(0, "♪ 正在播放 ♪", null),
            LyricLine(2000, songTitle, null),
            LyricLine(4000, "演唱：$artist", null),
            LyricLine(6000, "", null),
            LyricLine(8000, noLyricText, null),
            LyricLine(10000, "请放松心情，享受音乐时光", null),
            LyricLine(12000, "", null),
            LyricLine(14000, "♫ 让音乐洗涤心灵 ♫", null)
        )
    }

    /**
     * 解析歌词信息为LyricLine列表
     */
    private fun parseLyricInfoToLines(lyricInfo: Any): List<LyricLine> {
        return try {
            when (lyricInfo) {
                is String -> {
                    // 如果是字符串，使用简单的歌词解析
                    // 避免在非suspend函数中调用suspend函数
                    parseLrcString(lyricInfo)
                }
                is com.example.aimusicplayer.data.model.LyricInfo -> {
                    // 如果是LyricInfo对象，直接转换
                    lyricInfo.entries.map { kotlinLine ->
                        LyricLine(
                            time = kotlinLine.time,
                            text = kotlinLine.text,
                            translation = kotlinLine.translation
                        )
                    }
                }
                else -> {
                    Log.w(TAG, "未知的歌词信息类型: ${lyricInfo.javaClass}")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析歌词失败", e)
            emptyList()
        }
    }

    /**
     * 解析LRC格式字符串（同步版本）
     */
    private fun parseLrcString(lrcContent: String): List<LyricLine> {
        val lyricLines = mutableListOf<LyricLine>()

        try {
            val lines = lrcContent.split("\n", "\r\n")
            val timePattern = Pattern.compile("\\[(\\d{2}):(\\d{2})\\.(\\d{2,3})\\]")

            for (line in lines) {
                val trimmedLine = line.trim()
                if (trimmedLine.isEmpty()) continue

                val matcher = timePattern.matcher(trimmedLine)
                val times = mutableListOf<Long>()

                // 提取所有时间标签
                while (matcher.find()) {
                    val minutes = matcher.group(1)?.toInt() ?: 0
                    val seconds = matcher.group(2)?.toInt() ?: 0
                    val milliseconds = matcher.group(3)?.let {
                        if (it.length == 2) it.toInt() * 10 else it.toInt()
                    } ?: 0

                    val timeMs = (minutes * 60 * 1000 + seconds * 1000 + milliseconds).toLong()
                    times.add(timeMs)
                }

                // 提取歌词文本
                val text = trimmedLine.replace(Regex("\\[\\d{2}:\\d{2}\\.\\d{2,3}\\]"), "").trim()

                // 为每个时间标签创建歌词行
                for (time in times) {
                    if (text.isNotEmpty()) {
                        lyricLines.add(LyricLine(time, text, null))
                    }
                }
            }

            // 按时间排序
            lyricLines.sortBy { it.time }
        } catch (e: Exception) {
            Log.e(TAG, "解析LRC字符串失败", e)
        }

        return lyricLines
    }

    /**
     * 更新歌词位置 - 优化版本，只支持LyricView
     */
    private fun updateLyricPosition(position: Long) {
        // 只使用LyricView，提高效率
        val lyricView = findLyricViewInViewPager()
        if (lyricView != null) {
            // 更新LyricView的当前行
            lyricView.updateCurrentLine(position, true)
        } else {
            Log.w(TAG, "LyricView未找到，无法更新歌词位置")
        }
    }

    /**
     * 在ViewPager2中查找LyricView
     */
    private fun findLyricViewInViewPager(): LyricView? {
        return try {
            // 尝试从ViewPager2中获取当前页面的LyricView
            val viewPager = binding.viewPagerPlayer
            val adapter = viewPager.adapter as? PlayerPagerAdapter

            if (adapter != null) {
                // 获取当前Fragment
                val currentFragment = adapter.getCurrentFragment()
                if (currentFragment is LyricPageFragment) {
                    return currentFragment.getLyricView()
                }
            }

            Log.w(TAG, "无法从ViewPager2中获取LyricView")
            null
        } catch (e: Exception) {
            Log.e(TAG, "查找LyricView失败", e)
            null
        }
    }

    /**
     * 更新背景颜色
     */
    private fun updateBackgroundColor(color: Int) {
        // 取消之前的动画
        backgroundColorAnimator?.cancel()

        // 获取当前背景颜色
        val currentColor = (binding.root.background as? ColorDrawable)?.color ?: Color.BLACK

        // 创建颜色过渡动画
        backgroundColorAnimator = android.animation.ValueAnimator.ofArgb(currentColor, color).apply {
            duration = BACKGROUND_ANIMATION_DURATION
            interpolator = android.view.animation.DecelerateInterpolator()
            addUpdateListener { animator ->
                val animatedColor = animator.animatedValue as Int
                binding.root.setBackgroundColor(animatedColor)
            }
            start()
        }
    }

    /**
     * 更新模糊背景
     * @param bitmap 模糊背景位图
     * @param withAnimation 是否添加动画效果
     */
    private fun updateBlurredBackground(bitmap: Bitmap, withAnimation: Boolean = false) {
        if (withAnimation) {
            // 先淡出当前背景
            binding.backgroundBlur.animate()
                .alpha(0f)
                .setDuration(COVER_ANIMATION_DURATION / 2)
                .withEndAction {
                    // 设置新背景
                    binding.backgroundBlur.setImageBitmap(bitmap)

                    // 淡入新背景
                    binding.backgroundBlur.animate()
                        .alpha(0.8f)
                        .setDuration(COVER_ANIMATION_DURATION / 2)
                        .start()
                }
                .start()
        } else {
            // 直接设置模糊背景
            binding.backgroundBlur.setImageBitmap(bitmap)
        }
    }

    /**
     * 播放歌曲切换动画 - 使用优化的动画工具
     */
    private fun playSongTransitionAnimation() {
        // 取消之前的动画
        songTransitionAnimator?.cancel()

        // 添加唱臂切换效果 - 使用新增的switchTrack方法
        binding.albumCoverView.switchTrack()

        // 为专辑封面添加缩放动画
        AnimationUtils.scale(binding.albumCoverView, 1f, 0.9f, 200) {
            AnimationUtils.scale(binding.albumCoverView, 0.9f, 1f, 200)
        }

        // 为歌曲标题添加滑动动画
        AnimationUtils.slideOut(binding.songTitle, -50f, 200, false) {
            AnimationUtils.slideIn(binding.songTitle, 50f, 200)
        }

        // 为艺术家信息添加淡出淡入动画
        AnimationUtils.fadeOut(binding.songArtist, 200, false) {
            AnimationUtils.fadeIn(binding.songArtist, 200)
        }

        // 为播放控制按钮添加心跳动画
        AnimationUtils.heartbeat(binding.buttonPlayerPlayPause, 1.1f, 400)
    }

    /**
     * 开始专辑旋转动画
     */
    private fun startAlbumRotation() {
        // 使用AlbumCoverView的动画
        binding.albumCoverView.start()

        // 取消之前的动画
        albumRotationAnimator?.cancel()

        // 保存当前旋转角度
        currentRotation = binding.vinylBackground.rotation

        // 创建黑胶唱片旋转动画（备用）
        val vinylAnimator = ObjectAnimator.ofFloat(binding.vinylBackground, View.ROTATION, currentRotation, currentRotation + 360f).apply {
            duration = ALBUM_ROTATION_DURATION
            repeatCount = ObjectAnimator.INFINITE
            interpolator = LinearInterpolator()
        }

        // 创建专辑封面旋转动画（备用）
        @Suppress("UNUSED_VARIABLE")
        val albumAnimator = ObjectAnimator.ofFloat(binding.albumArt, View.ROTATION, currentRotation, currentRotation + 360f).apply {
            duration = ALBUM_ROTATION_DURATION
            repeatCount = ObjectAnimator.INFINITE
            interpolator = LinearInterpolator()
        }

        // 保存动画引用
        albumRotationAnimator = vinylAnimator
    }

    /**
     * 暂停专辑旋转动画
     */
    private fun pauseAlbumRotation() {
        // 使用AlbumCoverView的暂停方法
        binding.albumCoverView.pause()

        // 保存当前旋转角度
        currentRotation = binding.vinylBackground.rotation

        // 暂停动画
        albumRotationAnimator?.pause()
    }

    /**
     * 重置专辑旋转动画
     */
    private fun resetAlbumRotation() {
        // 使用AlbumCoverView的重置方法
        binding.albumCoverView.reset()

        // 取消之前的动画
        albumRotationAnimator?.cancel()

        // 重置旋转角度
        binding.vinylBackground.rotation = 0f
        binding.albumArt.rotation = 0f
        currentRotation = 0f
    }

    /**
     * 切换歌词/封面显示模式
     */
    private fun toggleLyricMode() {
        isLyricMode = !isLyricMode

        if (isLyricMode) {
            // 切换到歌词模式
            binding.albumArt.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction {
                    binding.albumArt.visibility = View.GONE
                    binding.vinylBackground.visibility = View.GONE
                    binding.viewPagerPlayer.visibility = View.VISIBLE
                    binding.viewPagerPlayer.alpha = 0f
                    binding.viewPagerPlayer.animate()
                        .alpha(1f)
                        .setDuration(300)
                        .start()
                }
                .start()
        } else {
            // 切换到封面模式
            binding.viewPagerPlayer.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction {
                    binding.viewPagerPlayer.visibility = View.GONE
                    binding.albumArt.visibility = View.VISIBLE
                    binding.vinylBackground.visibility = View.VISIBLE
                    binding.albumArt.alpha = 0f
                    binding.albumArt.animate()
                        .alpha(1f)
                        .setDuration(300)
                        .start()
                }
                .start()
        }
    }

    /**
     * 更新文本颜色
     */
    private fun updateTextColor(color: Int) {
        // 更新文本颜色
        binding.songTitle.setTextColor(color)
        binding.songArtist.setTextColor(ColorUtils.setAlphaComponent(color, 200))
        binding.textviewPlayerCurrentTime.setTextColor(color)
        binding.textviewPlayerTotalTime.setTextColor(color)
    }

    /**
     * 更新收藏按钮状态
     */
    private fun updateCollectButton(isCollected: Boolean) {
        binding.buttonPlayerCollect.isSelected = isCollected
    }

    /**
     * 切换收藏状态 - 增强版本，支持动画和反馈
     */
    private fun toggleCollect() {
        // 获取当前歌曲
        val currentSong = viewModel.currentSong.value
        if (currentSong == null) {
            showSnackbar("没有正在播放的歌曲")
            return
        }

        // 获取当前收藏状态
        val isCurrentlyCollected = viewModel.isCurrentSongCollected.value ?: false

        // 添加收藏按钮动画
        animateCollectButton(!isCurrentlyCollected)

        // 切换收藏状态
        viewModel.toggleCollect()

        // 显示反馈信息
        val message = if (isCurrentlyCollected) {
            "已取消收藏「${currentSong.mediaMetadata.title}」"
        } else {
            "已收藏「${currentSong.mediaMetadata.title}」"
        }
        showSnackbar(message)

        // 添加触觉反馈
        addHapticFeedback()
    }

    /**
     * 收藏按钮动画 - 增强版本，支持心跳和颜色切换效果
     */
    private fun animateCollectButton(isCollected: Boolean) {
        val button = binding.buttonPlayerCollect

        // 第一阶段：放大动画
        button.animate()
            .scaleX(1.3f)
            .scaleY(1.3f)
            .setDuration(150)
            .setInterpolator(android.view.animation.OvershootInterpolator(2f))
            .withEndAction {
                // 更新按钮状态（切换图标颜色）
                button.isSelected = isCollected

                // 第二阶段：回弹动画
                button.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(200)
                    .setInterpolator(android.view.animation.BounceInterpolator())
                    .withEndAction {
                        // 第三阶段：轻微心跳效果
                        if (isCollected) {
                            button.animate()
                                .scaleX(1.1f)
                                .scaleY(1.1f)
                                .setDuration(100)
                                .withEndAction {
                                    button.animate()
                                        .scaleX(1f)
                                        .scaleY(1f)
                                        .setDuration(100)
                                        .start()
                                }
                                .start()
                        }
                    }
                    .start()
            }
            .start()
    }

    /**
     * 通用按钮点击动画效果 - 替代波纹效果
     */
    private fun addButtonClickEffect(view: View, scaleRatio: Float = 0.9f) {
        // 取消之前的动画
        view.animate().cancel()

        // 按下效果：缩小
        view.animate()
            .scaleX(scaleRatio)
            .scaleY(scaleRatio)
            .setDuration(100)
            .setInterpolator(android.view.animation.AccelerateInterpolator())
            .withEndAction {
                // 释放效果：恢复并轻微放大
                view.animate()
                    .scaleX(1.05f)
                    .scaleY(1.05f)
                    .setDuration(100)
                    .setInterpolator(android.view.animation.DecelerateInterpolator())
                    .withEndAction {
                        // 最终恢复正常大小
                        view.animate()
                            .scaleX(1f)
                            .scaleY(1f)
                            .setDuration(100)
                            .setInterpolator(android.view.animation.AccelerateDecelerateInterpolator())
                            .start()
                    }
                    .start()
            }
            .start()

        // 添加触觉反馈
        addHapticFeedback()
    }

    /**
     * 添加触觉反馈
     */
    private fun addHapticFeedback() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                @Suppress("DEPRECATION")
                val vibrator = requireActivity().getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                vibrator.vibrate(VibrationEffect.createOneShot(30, VibrationEffect.DEFAULT_AMPLITUDE))
            }
        } catch (e: Exception) {
            Log.e(TAG, "触觉反馈失败", e)
        }
    }







    /**
     * 更新播放/暂停按钮状态，带流畅动画
     */
    private fun updatePlayPauseButton(isPlaying: Boolean) {
        val button = binding.buttonPlayerPlayPause

        // 如果状态没有变化，不执行动画
        if (button.isSelected == isPlaying) return

        // 添加旋转缩放动画
        button.animate()
            .scaleX(0.8f)
            .scaleY(0.8f)
            .rotation(button.rotation + 180f)
            .setDuration(150)
            .withEndAction {
                // 更新按钮状态
                button.isSelected = isPlaying

                // 恢复缩放，继续旋转
                button.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .rotation(button.rotation + 180f)
                    .setDuration(150)
                    .start()
            }
            .start()
    }

    /**
     * 更新播放模式按钮
     */
    private fun updatePlayModeButton(mode: PlayMode) {
        when (mode) {
            PlayMode.Loop -> {
                binding.buttonPlayerPlayMode.setImageResource(R.drawable.ic_repeat)
                binding.buttonPlayerPlayMode.contentDescription = getString(R.string.play_mode_loop)
            }
            PlayMode.Single -> {
                binding.buttonPlayerPlayMode.setImageResource(R.drawable.ic_repeat_one)
                binding.buttonPlayerPlayMode.contentDescription = getString(R.string.play_mode_single)
            }
            PlayMode.Shuffle -> {
                binding.buttonPlayerPlayMode.setImageResource(R.drawable.ic_shuffle)
                binding.buttonPlayerPlayMode.contentDescription = getString(R.string.play_mode_shuffle)
            }
        }
    }



    /**
     * 显示播放列表对话框
     * 增强版：添加动画效果
     */
    private fun showPlaylistDialog() {
        // 创建底部对话框
        val dialog = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val dialogView = layoutInflater.inflate(R.layout.dialog_playlist, null)
        dialog.setContentView(dialogView)

        // 设置动画效果
        setupDialogAnimation(dialog)

        // 获取RecyclerView
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recycler_view_playlist)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // 获取播放列表数量文本
        val countTextView = dialogView.findViewById<TextView>(R.id.text_playlist_count)

        // 获取空播放列表提示
        val emptyView = dialogView.findViewById<TextView>(R.id.text_empty_playlist)

        // 获取操作按钮
        val clearButton = dialogView.findViewById<Button>(R.id.button_clear_playlist)
        val shuffleButton = dialogView.findViewById<Button>(R.id.button_shuffle_playlist)

        // 创建增强型适配器，支持删除和拖拽排序
        val adapter = MediaItemAdapter(emptyList()) { position ->
            // 点击播放列表项
            viewModel.playAtIndex(position)
            dialog.dismiss()
        }

        // 设置长按删除功能
        adapter.setOnItemLongClickListener { position, mediaItem ->
            showDeleteSongDialog(mediaItem, position) {
                // 删除成功后刷新列表
                adapter.notifyItemRemoved(position)
            }
        }

        recyclerView.adapter = adapter

        // 观察播放列表
        viewModel.playQueue.observe(viewLifecycleOwner) { mediaItems ->
            if (mediaItems.isNotEmpty()) {
                // 更新播放列表数量
                countTextView.text = "(${mediaItems.size}首)"

                // 更新适配器数据
                adapter.updateMediaItems(mediaItems)

                // 显示RecyclerView和操作按钮，隐藏空提示
                recyclerView.visibility = View.VISIBLE
                clearButton?.visibility = View.VISIBLE
                shuffleButton?.visibility = View.VISIBLE
                emptyView.visibility = View.GONE
            } else {
                // 显示空提示，隐藏RecyclerView和操作按钮
                recyclerView.visibility = View.GONE
                clearButton?.visibility = View.GONE
                shuffleButton?.visibility = View.GONE
                emptyView.visibility = View.VISIBLE
            }
        }

        // 设置清空播放列表按钮
        clearButton?.setOnClickListener {
            showClearPlaylistDialog {
                viewModel.clearPlaylist()
                dialog.dismiss()
            }
        }

        // 设置随机播放按钮
        shuffleButton?.setOnClickListener {
            viewModel.shufflePlaylist()
            showSnackbar("播放列表已随机排序")
        }

        // 获取关闭按钮
        val closeButton = dialogView.findViewById<ImageButton>(R.id.button_playlist_close)

        // 设置关闭按钮点击事件
        closeButton.setOnClickListener {
            dialog.dismiss()
        }

        // 显示对话框
        dialog.show()
    }

    /**
     * 显示删除歌曲确认对话框
     */
    private fun showDeleteSongDialog(mediaItem: androidx.media3.common.MediaItem, position: Int, onConfirm: () -> Unit) {
        AlertDialog.Builder(requireContext())
            .setTitle("删除歌曲")
            .setMessage("确定要从播放列表中删除「${mediaItem.mediaMetadata.title}」吗？")
            .setPositiveButton("删除") { _, _ ->
                viewModel.removeFromPlaylist(position)
                onConfirm()
                showSnackbar("已从播放列表中删除")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示清空播放列表确认对话框
     */
    private fun showClearPlaylistDialog(onConfirm: () -> Unit) {
        AlertDialog.Builder(requireContext())
            .setTitle("清空播放列表")
            .setMessage("确定要清空整个播放列表吗？此操作不可撤销。")
            .setPositiveButton("清空") { _, _ ->
                onConfirm()
                showSnackbar("播放列表已清空")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示Snackbar提示
     */
    private fun showSnackbar(message: String) {
        try {
            Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
                .setBackgroundTint(ContextCompat.getColor(requireContext(), R.color.theme_primary))
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.text_light))
                .show()
        } catch (e: Exception) {
            Log.e(TAG, "显示Snackbar失败", e)
        }
    }

    /**
     * 显示评论页面
     * 使用Navigation导航到CommentFragment
     */
    private fun showCommentDialog() {
        // 检查当前是否有歌曲在播放
        if (viewModel.currentSong.value == null) {
            Toast.makeText(requireContext(), "请先播放一首歌曲", Toast.LENGTH_SHORT).show()
            return
        }

        // 获取当前歌曲ID和名称
        val songId = viewModel.currentSong.value?.mediaId?.toLongOrNull() ?: return
        val songName = viewModel.currentSong.value?.mediaMetadata?.title?.toString() ?: ""

        // 使用Navigation导航到CommentFragment
        val action = PlayerFragmentDirections.actionPlayerFragmentToCommentFragment(
            songId = songId,
            songName = songName
        )
        findNavController().navigate(action)
    }

    /**
     * 显示心动模式
     * 使用Navigation导航到IntelligenceFragment
     */
    private fun showHeartModeDialog() {
        // 检查当前是否有歌曲在播放
        if (viewModel.currentSong.value == null) {
            Toast.makeText(requireContext(), "请先播放一首歌曲", Toast.LENGTH_SHORT).show()
            return
        }

        // 获取当前歌曲ID
        val songId = viewModel.currentSong.value?.mediaId?.toLongOrNull() ?: return

        // 使用Navigation导航到IntelligenceFragment
        val action = PlayerFragmentDirections.actionPlayerFragmentToIntelligenceFragment(
            songId = songId,
            playlistId = -1L
        )
        findNavController().navigate(action)
    }



    /**
     * 设置对话框动画效果
     */
    private fun setupDialogAnimation(dialog: BottomSheetDialog) {
        // 获取对话框内容视图
        val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.let {
            // 设置初始状态
            it.alpha = 0f
            it.scaleX = 0.8f
            it.scaleY = 0.8f

            // 创建动画
            it.animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(250)
                .setInterpolator(android.view.animation.OvershootInterpolator(0.8f))
                .start()

            // 设置对话框消失时的动画
            dialog.setOnDismissListener {
                bottomSheet.animate()
                    .alpha(0f)
                    .scaleX(0.8f)
                    .scaleY(0.8f)
                    .setDuration(200)
                    .start()
            }
        }
    }

    /**
     * 显示错误提示
     */
    private fun showErrorSnackbar(message: String) {
        try {
            Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
                .setBackgroundTint(ContextCompat.getColor(requireContext(), R.color.theme_error))
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.text_light))
                .show()
        } catch (e: Exception) {
            Log.e(TAG, "显示错误提示失败", e)
        }
    }

    /**
     * 显示错误弹窗
     */
    private fun showErrorDialog(title: String, message: String) {
        try {
            if (isAdded && !requireActivity().isFinishing) {
                AlertDialog.Builder(requireContext())
                    .setTitle(title)
                    .setMessage(message)
                    .setPositiveButton("确定") { dialog, _ ->
                        dialog.dismiss()
                    }
                    .setCancelable(true)
                    .show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "显示错误弹窗失败", e)
        }
    }

    /**
     * 优化触摸反馈
     */
    private fun addTouchFeedback(view: View) {
        view.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 按下时缩放
                    v.animate()
                        .scaleX(0.95f)
                        .scaleY(0.95f)
                        .setDuration(100)
                        .start()

                    // 触觉反馈
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        @Suppress("DEPRECATION")
                        val vibrator = requireActivity().getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                        vibrator.vibrate(VibrationEffect.createOneShot(10, VibrationEffect.DEFAULT_AMPLITUDE))
                    }
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 释放时恢复
                    v.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(100)
                        .start()
                }
            }
            false
        }
    }

    /**
     * 设置搜索功能
     */
    private fun setupSearchFunction() {
        // 初始化搜索建议适配器
        searchSuggestionsAdapter = SearchSuggestionsAdapter { suggestion ->
            // 点击建议项，设置文本并执行搜索
            Log.d(TAG, "点击搜索建议: $suggestion")
            binding.searchEditText.setText(suggestion)
            binding.searchEditText.setSelection(suggestion.length) // 设置光标到末尾
            performSearch(suggestion)
            hideKeyboard()
        }

        // 初始化搜索结果适配器
        searchResultsAdapter = SearchResultsAdapter { song ->
            // 点击搜索结果，播放歌曲
            Log.d(TAG, "点击搜索结果: ${song.name} - ${song.getArtistNames()}, ID: ${song.id}")
            try {
                viewModel.playSearchResult(song)
                hideSearchResults()
                hideKeyboard()
                // 收缩搜索框
                collapseSearchBox()
            } catch (e: Exception) {
                Log.e(TAG, "播放搜索结果失败", e)
                showErrorSnackbar("播放失败，请重试")
            }
        }

        // 设置RecyclerView
        binding.searchSuggestionsRecycler.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = searchSuggestionsAdapter
        }

        binding.searchResultsRecycler.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = searchResultsAdapter
        }

        // 搜索框文本变化监听
        binding.searchEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val query = s?.toString()?.trim() ?: ""
                if (query.isNotEmpty()) {
                    // 用户输入时显示搜索建议，不显示搜索结果
                    viewModel.getSearchSuggestions(query)
                    hideSearchResults() // 隐藏之前的搜索结果
                } else {
                    // 清空建议和结果
                    viewModel.clearSearchResults()
                    hideSearchSuggestions()
                    hideSearchResults()
                }
            }

            override fun afterTextChanged(s: Editable?) {}
        })

        // 搜索框焦点变化监听
        binding.searchEditText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                expandSearchBox()
            } else {
                if (binding.searchEditText.text.toString().trim().isEmpty()) {
                    collapseSearchBox()
                }
            }
        }

        // 搜索框按键监听（Enter键搜索）
        binding.searchEditText.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)) {
                val query = binding.searchEditText.text.toString().trim()
                if (query.isNotEmpty()) {
                    performSearch(query)
                    hideKeyboard()
                }
                true
            } else {
                false
            }
        }

        // 搜索按钮点击监听
        binding.searchButton.setOnClickListener {
            if (binding.searchEditText.visibility == View.GONE) {
                // 显示并展开搜索框
                showAndExpandSearchBox()
            } else {
                val query = binding.searchEditText.text.toString().trim()
                if (query.isNotEmpty()) {
                    performSearch(query)
                    hideKeyboard()
                } else {
                    // 如果搜索框为空，聚焦到搜索框
                    binding.searchEditText.requestFocus()
                    showKeyboard()
                }
            }
        }
    }

    /**
     * 执行搜索
     */
    private fun performSearch(query: String) {
        Log.d(TAG, "执行搜索: $query")
        // 隐藏搜索建议，显示搜索结果
        hideSearchSuggestions()
        // 执行搜索，结果会通过观察者自动显示
        viewModel.searchSongs(query)
    }

    /**
     * 显示并展开搜索框
     */
    private fun showAndExpandSearchBox() {
        binding.searchEditText.visibility = View.VISIBLE
        expandSearchBox()
        binding.searchEditText.requestFocus()
        showKeyboard()
    }

    /**
     * 展开搜索框
     */
    private fun expandSearchBox() {
        if (!isSearchExpanded) {
            isSearchExpanded = true
            // 展开搜索框宽度动画
            val searchEditText = binding.searchEditText
            val expandedWidth = (resources.displayMetrics.widthPixels * 0.4).toInt()

            val animator = ValueAnimator.ofInt(searchEditText.width, expandedWidth)
            animator.duration = 300
            animator.addUpdateListener { animation ->
                val animatedValue = animation.animatedValue as Int
                val layoutParams = searchEditText.layoutParams
                layoutParams.width = animatedValue
                searchEditText.layoutParams = layoutParams
            }
            animator.start()
            Log.d(TAG, "搜索框已展开")
        }
    }

    /**
     * 收缩搜索框
     */
    private fun collapseSearchBox() {
        if (isSearchExpanded) {
            isSearchExpanded = false
            hideSearchSuggestions()
            hideSearchResults()

            // 收缩搜索框宽度动画
            val searchEditText = binding.searchEditText
            val collapsedWidth = resources.getDimensionPixelSize(R.dimen.search_box_collapsed_width)

            val animator = ValueAnimator.ofInt(searchEditText.width, collapsedWidth)
            animator.duration = 300
            animator.addUpdateListener { animation ->
                val animatedValue = animation.animatedValue as Int
                val layoutParams = searchEditText.layoutParams
                layoutParams.width = animatedValue
                searchEditText.layoutParams = layoutParams
            }
            animator.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    if (binding.searchEditText.text.toString().trim().isEmpty()) {
                        binding.searchEditText.visibility = View.GONE
                    }
                }
            })
            animator.start()
            Log.d(TAG, "搜索框已收缩")
        }
    }

    /**
     * 隐藏搜索建议
     */
    private fun hideSearchSuggestions() {
        binding.searchSuggestionsRecycler.visibility = View.GONE
    }

    /**
     * 隐藏搜索结果
     */
    private fun hideSearchResults() {
        binding.searchResultsRecycler.visibility = View.GONE
        binding.viewPagerPlayer.visibility = View.VISIBLE
    }

    /**
     * 显示键盘
     */
    private fun showKeyboard() {
        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(binding.searchEditText, InputMethodManager.SHOW_IMPLICIT)
    }

    /**
     * 隐藏键盘
     */
    private fun hideKeyboard() {
        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(binding.searchEditText.windowToken, 0)
        binding.searchEditText.clearFocus()
    }

    override fun onDestroyView() {
        super.onDestroyView()

        try {
            // 取消颜色动画
            backgroundColorAnimator?.cancel()
            backgroundColorAnimator = null

            // 取消专辑旋转动画
            albumRotationAnimator?.cancel()
            albumRotationAnimator = null

            // 取消歌曲切换动画
            songTransitionAnimator?.cancel()
            songTransitionAnimator = null

            // 清理缓存
            lifecycleScope.launch {
                try {
                    enhancedImageCache.cleanExpiredCache()
                } catch (e: Exception) {
                    Log.e(TAG, "清理缓存失败", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "销毁视图时发生错误", e)
        } finally {
            _binding = null
        }
    }
}
