# 轻聆 App - 核心流程与开发指南

本文档旨在概述"轻聆"智能车载音乐播放器的核心应用流程，并整合开发过程中需遵循的关键指示和规范，作为AI辅助开发的指导性参考。

## 一、 应用整体流程

应用的核心流程围绕用户从未登录到登录，再到主界面的各项功能模块的使用和切换。所有页面均不显示顶部标题栏，并尽可能实现全屏体验。

```mermaid
graph TD
    A[启动应用] --> B[SplashActivity: 显示欢迎页];
    B -- 短暂显示后 --> C{检查持久化登录状态};
    C -- 已登录 --> E_PrepareMain[准备进入主界面]; 
    C -- 未登录 --> D[LoginActivity: 显示登录选项];

    subgraph LoginActivity Process
        D --> D_QR[按钮: 二维码登录];
        D --> D_Phone[按钮: 手机号登录];
        D --> D_Guest[按钮: 游客登录];

        D_QR -- 点击 --> D_QR_Dialog[弹出 QrLoginDialog];
        D_Phone -- 点击 --> D_Phone_Dialog[弹出 PhoneLoginDialog];

        D_QR_Dialog -- 登录成功 (API) --> LoginSuccess;
        D_Phone_Dialog -- 登录成功 (API) --> LoginSuccess;
        
        D_Guest -- 点击 --> CallGuestLoginAPI[调用游客登录API (来自api.txt, 必须成功)];
        CallGuestLoginAPI -- API调用成功 --> LoginSuccess;
        CallGuestLoginAPI -- API缺失/调用失败 --> GuestLoginError[显示错误提示, 停在登录页];
        GuestLoginError --> D;

        LoginSuccess[登录成功/游客API成功进入] -- 保存状态 --> E_PrepareMain; // 登录后准备进入主界面
        D_QR_Dialog -- 关闭/失败 --> D;
        D_Phone_Dialog -- 关闭/失败 --> D;
    end
    
    E_PrepareMain --> E_StartActivity[启动 MainActivity];
    
    subgraph MainActivity Lifecycle and Permissions
        E_StartActivity --> E_OnCreate[MainActivity onCreate];
        E_OnCreate --> E_CheckPerms{检查运行时权限? (存储/媒体, 录音...)};
        E_CheckPerms -- 未授予 --> E_RequestPerms[请求权限];
        E_RequestPerms -- 用户响应 --> E_HandlePermResult{处理权限结果};
        E_HandlePermResult -- 拒绝 --> E_ShowRationaleOrBlock[显示理由/阻止功能/退出?];
        E_CheckPerms -- 已授予 --> E_LoadDefaultView[加载默认视图 PlayerFragment];
        E_HandlePermResult -- 授予 --> E_LoadDefaultView;
    end

    subgraph MainActivity with Sidebar Navigation
        E_LoadDefaultView --> E_Base[MainActivity 核心];
        E_Base --> Sidebar[侧边导航栏 (可自动隐藏/滑出)];
        E_Base --> ContentArea[Fragment容器];

        Sidebar -- 点击 '播放器' --> E_Player[PlayerFragment 显示在 ContentArea];
        Sidebar -- 点击 '我的音乐库' --> E_Library[MusicLibraryFragment 显示在 ContentArea];
        Sidebar -- 点击 '音乐探索' --> E_Discovery[DiscoveryFragment 显示在 ContentArea];
        Sidebar -- 点击 '驾驶模式' --> E_Driving[DrivingModeFragment 显示在 ContentArea];
        Sidebar -- 点击 '用户中心' --> E_Profile[UserProfileFragment 显示在 ContentArea];
        Sidebar -- 点击 '设置' --> E_Settings[SettingsFragment 显示在 ContentArea];
    end

    subgraph PlayerFragment Details
        E_Player --> PF_ServiceInteraction[与统一播放服务交互];
        E_Player --> PF_Info[显示: 封面, 歌名, 歌手, 歌词, 进度];
        E_Player --> PF_Controls[播放控制: 列表, **收藏**, 上/下曲, 播放/暂停, 模式];
    end

    subgraph MusicLibraryFragment Details
        E_Library --> ML_ServiceInteraction[与统一播放服务交互 (用于播放)];
        E_Library --> ML_Search[搜索框];
        E_Library --> ML_Tabs[TabLayout: **本地**, 歌曲, 专辑, 歌手, 歌单 (API依赖)];
        ML_Tabs -- 选择Tab --> ML_List[RecyclerView: 显示对应列表 (各项带"更多操作"菜单)];
        ML_List -- "更多操作"点击 --> ML_MoreActionsDetail[处理具体操作及API依赖];
    end

    subgraph MusicDiscoveryFragment Details
        E_Discovery --> Disc_ServiceInteraction[与统一播放服务交互 (用于播放)];
        E_Discovery --> Disc_Search[搜索框 (API依赖)];
        E_Discovery --> Disc_Sections[内容区域: 推荐, 榜单, 新歌等 (各项带"更多操作", 各含"查看更多", API依赖)];
        Disc_Sections -- "更多操作"点击 --> Disc_MoreActionsDetail[处理具体操作及API依赖];
    end

    subgraph DrivingModeFragment Details
        E_Driving --> DM_CheckAutoVoice[检查设置: 是否自动开启语音?];
        DM_CheckAutoVoice -- 是 --> DM_ActivateVoice[自动激活语音模式];
        DM_CheckAutoVoice -- 否 --> DM_NormalStart;
        DM_ActivateVoice --> DM_NormalStart[正常启动驾驶模式UI];
        DM_NormalStart --> DM_ServiceInteraction[与统一播放服务交互];
        DM_NormalStart --> DM_PlayerInfo[简化播放信息 + 大号控制];
        DM_NormalStart --> DM_QuickActions[快捷操作: 语音(手动), 列表, 顺序, 音量];
    end

    subgraph UserProfileFragment Details
        E_Profile --> UP_Display[用户资料与统计信息 (API依赖)];
    end

    E_Settings --> Set_Options[设置分类与选项 (语言选择提供实际机制)];

    click A "sección_1_1"
    click B "sección_1_1"
    click C "sección_1_2_check"
    click D "sección_1_2"
    click D_QR "sección_1_2_qr"
    click D_Phone "sección_1_2_phone"
    click D_Guest "sección_1_2_guest"
    click D_QR_Dialog "sección_1_2_qr_dialog"
    click D_Phone_Dialog "sección_1_2_phone_dialog"
    click LoginSuccess "sección_1_2_success"
    click E "sección_1_3"
    click Sidebar "sección_1_3_sidebar"
    click ContentArea "sección_1_3_content"
    click E_Player "sección_1_4_1"
    click E_Library "sección_1_4_2"
    click E_Discovery "sección_1_4_3"
    click E_Driving "sección_1_4_4"
    click E_Profile "sección_1_4_5"
    click E_Settings "sección_1_4_6"
    click PF_Controls "sección_1_4_1_controls"
    click ML_Tabs "sección_1_4_2_tabs"
    click ML_MoreActionsDetail "sección_1_4_2_more_actions"
    click Disc_MoreActionsDetail "sección_1_4_3_more_actions"
    click PF_ServiceInteraction "sección_architecture_playback_service"
    click ML_ServiceInteraction "sección_architecture_playback_service"
    click Disc_ServiceInteraction "sección_architecture_playback_service"
    click DM_ServiceInteraction "sección_architecture_playback_service"
```
*(这是一个Mermaid语法的流程图描述，可在支持Mermaid的Markdown编辑器中渲染查看)*

### 1.1. 启动欢迎页 (ID: `sección_1_1`)
*   **功能**: 应用首次启动时展示 (`SplashActivity`)，包含应用Logo、名称和Slogan。**无顶部标题栏**。
*   **核心组件/文件**:
    *   `Activity`: `SplashActivity.java`
    *   `Layout`: `activity_splash.xml`
    *   `Drawable`: 应用Logo资源
*   **流程**: 短暂显示后自动跳转，检查用户登录状态。

### 1.2. 用户登录流程 (ID: `sección_1_2_check`, `sección_1_2`)
*   **检查登录状态 (ID: `sección_1_2_check`)**: `SplashActivity` 或工具类检查持久化的登录状态。
    *   若已登录，跳转至 `MainActivity` (`sección_1_3`)。
    *   若未登录，跳转至 `LoginActivity` (`sección_1_2`)。
*   **登录页 (`LoginActivity`) (ID: `sección_1_2`)**:
    *   **功能**: 若用户未登录，则引导用户登录。**无顶部标题栏**。
    *   **核心组件/文件**:
        *   `Activity`: `LoginActivity.java`
        *   `Layout`: `activity_login.xml` (二维码、手机号、游客登录)
        *   `Dialogs`: `QrLoginDialog.java` (及对应布局), `PhoneLoginDialog.java` (及对应布局)
        *   视图元素ID: `button_qr_login`, `button_phone_login`, `button_guest_login`
    *   **流程**:
        *   **二维码登录 (ID: `sección_1_2_qr`, `sección_1_2_qr_dialog`)**: 点击 `button_qr_login` 弹出 `QrLoginDialog`。成功需API验证。
        *   **手机号登录 (ID: `sección_1_2_phone`, `sección_1_2_phone_dialog`)**: 点击 `button_phone_login` 弹出 `PhoneLoginDialog`。成功需API验证。
        *   **游客登录 (ID: `sección_1_2_guest`)**: 点击 `button_guest_login`。
            *   **必须调用 `api.txt` 中定义的游客登录接口。**
            *   **开发前置检查**: 若 `api.txt` 未提供游客登录接口，此功能无法实现，UI上可能需禁用或隐藏此选项，并向开发者报告。
            *   若API接口存在但调用失败（网络错误、服务器错误码等），必须向用户显示明确的错误提示，不允许进入主界面，用户停留在登录页。
        *   **登录成功 (ID: `sección_1_2_success`)**: 二维码、手机号或游客API调用成功后，保存登录状态，然后**启动 `MainActivity`** (ID: `sección_start_main`)。

### 1.3. 主界面 (`MainActivity`) (ID: `sección_1_3`)
*   **功能**: 应用核心交互界面。**无顶部标题栏，全屏显示**。
*   **核心组件/文件**:
    *   `Activity`: `MainActivity.java`
    *   `Layout`: `activity_main.xml` (包含侧边栏和Fragment容器)
    *   `Fragments`: 各功能模块对应的Fragment。
*   **结构与交互**:
    *   **运行时权限处理 (ID: `sección_main_permission`)**: 
        *   `MainActivity` 在启动时（例如 `onCreate` 或 `onResume`）**必须检查应用所需的核心运行时权限**。这至少应包括：
            *   存储/媒体权限 (如 `READ_MEDIA_AUDIO` 或 `READ_EXTERNAL_STORAGE`，根据目标SDK和需求)。
            *   录音权限 (`RECORD_AUDIO`) 用于语音控制。
            *   通知权限 (`POST_NOTIFICATIONS`) (Android 13+)，如果需要后台播放通知。
        *   如果权限尚未授予，**必须发起权限请求**。
        *   **必须处理用户的授权结果**：
            *   **若用户授予**: 继续加载UI和功能。
            *   **若用户拒绝**: 需要根据权限的重要性决定如何响应。可能需要向用户解释为何需要该权限（Rationale），并再次请求；或者禁用依赖该权限的功能；对于核心权限（如存储访问对音乐播放器至关重要），如果用户持续拒绝，可能需要提示用户无法正常使用应用，甚至退出应用。
    *   **初始视图加载**: **在确认核心权限被授予后**，`MainActivity` **默认加载 `PlayerFragment`**。
    *   **侧边导航栏 (Sidebar) (ID: `sección_1_3_sidebar`)**: 
        *   垂直图标按钮，点击切换 `ContentArea` 中的 `Fragment`。
        *   **行为**: 长时间无用户操作时自动隐藏，可通过特定手势（如屏幕边缘滑出）或按钮重新显示。
    *   **内容区域 (Content Area) (ID: `sección_1_3_content`)**: `FrameLayout` 用于加载 `Fragment`。
    *   **统一播放服务交互**: `MainActivity` 可能需要初始化或绑定到统一播放服务，以便各Fragment可以访问和控制播放状态。
    *   **API依赖提示**: 对于上述任何依赖API的操作，若`api.txt`中未定义相应接口，则该具体操作功能暂停开发，并向开发者明确报告。

### 1.4. 主界面模块详情

#### 1.4.1. 播放器视图 (`PlayerFragment`) (ID: `sección_1_4_1`)
*   **功能**: 展示当前播放歌曲详情与控制。**无顶部标题栏**。
*   **核心交互**: 
    *   通过 **统一播放服务 (ID: `sección_architecture_playback_service`)** 获取和同步播放信息（封面、歌名、歌手、歌词、进度、播放状态等）。
    *   播放控制操作（列表、收藏、上/下曲、播放/暂停、模式切换 (ID `sección_1_4_1_controls`)）均通过统一播放服务执行。

#### 1.4.2. 我的音乐库视图 (`MusicLibraryFragment`) (ID: `sección_1_4_2`, `sección_1_4_2_more_actions`)
*   **功能**: 展示本地及用户收藏的音乐资源。**无顶部标题栏**。
*   **核心交互与API依赖**:
    *   **TabLayout (ID `sección_1_4_2_tabs`)**: "**本地音乐**"、"歌曲"、"专辑"、"歌手"、"歌单"。
        *   "本地音乐"：扫描本地存储。
        *   "歌曲"、"专辑"、"歌手"、"歌单"：**获取这些列表数据依赖于`api.txt`中定义的接口。若接口缺失，对应功能暂停开发并提示用户。**
    *   **RecyclerView**: 列表项点击播放时，通过 **统一播放服务** 处理。
    *   **列表项的"更多操作" (Context Menu)**: (ID: `sección_1_4_2_more_actions`)
        *   **"本地音乐"标签页 - 歌曲项**:
            *   `播放下一首`: (统一播放服务)。
            *   `添加到播放队列`: (统一播放服务)。
            *   `添加到歌单...`: 
                *   获取用户歌单列表: **依赖 `api.txt#getUserPlaylists` 接口。** (若缺，提示)
                *   添加到已选歌单/创建新歌单并添加: **依赖 `api.txt#addSongToPlaylist` / `api.txt#createPlaylistAndAddSong` 接口。** (若缺，提示)
            *   `查看歌曲信息`: 显示本地元数据。
            *   `从设备删除`: 本地文件删除 (需确认)。
        *   **"歌曲" (已收藏/云端)标签页 - 歌曲项**:
            *   `播放下一首`: (统一播放服务)。
            *   `添加到播放队列`: (统一播放服务)。
            *   `取消收藏`: **依赖 `api.txt#removeSongFromFavorites` 接口。** (若缺，提示)
            *   `添加到歌单...`: (同上，依赖歌单相关API)。
            *   `查看专辑信息`: **依赖 `api.txt#getAlbumDetails` 接口。** (若缺，提示)
            *   `查看歌手信息`: **依赖 `api.txt#getArtistDetails` 接口。** (若缺，提示)
            *   `查看歌曲详情`: **依赖 `api.txt#getSongDetails` 接口。** (若缺，提示)
        *   **"专辑"标签页 - 专辑项**:
            *   `播放该专辑`: (统一播放服务)。歌曲列表获取 **依赖 `api.txt#getAlbumSongs` 接口。** (若缺，提示)
            *   `添加到播放队列`: 歌曲列表获取 **依赖 `api.txt#getAlbumSongs` 接口。** (若缺，提示)
            *   `收藏专辑/取消收藏专辑`: **依赖 `api.txt#collectAlbum` / `api.txt#uncollectAlbum` 接口。** (若缺，提示)
        *   **"歌手"标签页 - 歌手项**:
            *   `播放该歌手的热门歌曲`: (统一播放服务)。歌曲列表获取 **依赖 `api.txt#getArtistTopSongs` 接口。** (若缺，提示)
            *   `关注歌手/取消关注歌手`: **依赖 `api.txt#followArtist` / `api.txt#unfollowArtist` 接口。** (若缺，提示)
            *   **"歌单"标签页 - 歌单项**:
                *   `播放该歌单`: (统一播放服务)。歌曲列表获取 **依赖 `api.txt#getPlaylistSongs` 接口。** (若缺，提示)
                *   `添加到播放队列`: 歌曲列表获取 **依赖 `api.txt#getPlaylistSongs` 接口。** (若缺，提示)
                *   `删除歌单` (用户创建的): **依赖 `api.txt#deletePlaylist` 接口。** (若缺，提示)
                *   `收藏歌单/取消收藏歌单` (推荐歌单): **依赖 `api.txt#collectPlaylist` / `api.txt#uncollectPlaylist` 接口。** (若缺，提示)
    *   **API依赖提示**: 对于上述任何依赖API的操作，若`api.txt`中未定义相应接口，则该具体操作功能暂停开发，并向开发者明确报告所缺少的接口。

#### 1.4.3. 音乐探索视图 (`DiscoveryFragment`) (ID: `sección_1_4_3`, `sección_1_4_3_more_actions`)
*   **功能**: 提供在线音乐发现。**无顶部标题栏**。
*   **核心交互与API依赖**:
    *   **搜索框、专属推荐、热门榜单、新歌速递、热门专辑等所有在线内容获取，以及"查看更多"导航到的完整列表页面，均依赖于`api.txt`中定义的接口。若接口缺失，对应功能暂停开发并提示用户。**
    *   内容项点击播放时，通过 **统一播放服务** 处理。
    *   **列表项的"更多操作" (Context Menu, 若提供)**: (ID: `sección_1_4_3_more_actions`)
        *   例如，推荐歌曲或榜单中的歌曲项:
            *   `播放下一首`: (统一播放服务)。
            *   `添加到播放队列`: (统一播放服务)。
            *   `收藏到我的音乐`: **依赖 `api.txt#addSongToFavorites` 接口。** (若缺，提示)
            *   `添加到歌单...`: (同1.4.2中歌曲项的"添加到歌单"逻辑，依赖歌单相关API)。
            *   `查看专辑信息`: **依赖 `api.txt#getAlbumDetails` 接口。** (若缺，提示)
            *   `查看歌手信息`: **依赖 `api.txt#getArtistDetails` 接口。** (若缺，提示)
        *   **API依赖提示**: 对于上述任何依赖API的操作，若`api.txt`中未定义相应接口，则该具体操作功能暂停开发，并向开发者明确报告。

#### 1.4.4. 驾驶模式视图 (`DrivingModeFragment`) (ID: `sección_1_4_4`)
*   **功能**: 驾驶场景优化的播放界面。**无顶部标题栏**。
*   **核心交互**: 
    *   **自动开启语音模式**: 当此 `Fragment` 变为可见时 (例如 `onResume`), **会检查"进入驾驶模式时自动开启语音模式"设置项的状态。如果设置为开启，则自动触发语音识别的激活逻辑**。
    *   所有播放信息展示和控制均通过 **统一播放服务** 同步和执行。
    *   快捷操作按钮提供手动触发相应功能的入口（包括手动激活语音模式）。

#### 1.4.5. 用户中心视图 (`UserProfileFragment`) (ID: `sección_1_4_5`)
*   **功能**: 展示用户信息。**无顶部标题栏**。
*   **核心交互与API依赖**: 
    *   **用户头像、昵称、签名、统计数据（收藏歌曲数、创建歌单数等）、账户详情（如会员状态、手机号绑定等）的获取和修改（若支持）均依赖于`api.txt`中定义的接口。若接口缺失，对应功能暂停开发并提示用户。**
    *   账户信息区域仅做展示，不包含"更多"入口。

#### 1.4.6. 应用设置视图 (`SettingsFragment`) (ID: `sección_1_4_6`)
*   **功能**: 提供应用的核心配置选项。**无顶部标题栏**。
*   **布局**: 由于设置项简化，不再需要左右分栏。可以直接使用一个垂直列表展示所有设置项。
*   **核心组件/文件**:
    *   `Fragment`: `SettingsFragment.java`
    *   `Layout`: `fragment_settings.xml` (包含设置项列表的布局)
    *   `RecyclerView` 或 `LinearLayout`: 用于展示设置项。
*   **核心交互**: 
    *   只包含以下"通用设置"项:
        *   **自动播放**: 开关，控制应用启动时是否自动播放上次的歌曲。状态本地持久化。
        *   **夜间模式**: 开关，切换应用整体主题。状态本地持久化，触发主题切换。
        *   **进入驾驶模式时自动开启语音模式**: 开关，**控制进入驾驶模式视图时是否自动开启语音模式**。状态本地持久化。
    *   移除其他设置分类和设置项。

## 二、 核心架构组件 (ID: `sección_architecture`)

### 2.1. 统一播放服务 (`UnifiedPlaybackService`) (ID: `sección_architecture_playback_service`)
*   **定位**: 后台服务 (`Service`)，应用内所有音乐播放的核心管理者。
*   **职责**:
    *   管理播放队列（添加、移除、排序歌曲）。
    *   控制音乐播放：播放、暂停、停止、上一首、下一首、Seek。
    *   实现播放模式逻辑：顺序播放、列表循环、单曲循环、随机播放 (参考 `car.html` 中的四种模式)。
    *   维护和广播当前播放状态：当前歌曲对象、播放进度、缓冲进度、播放/暂停状态、当前播放模式。
    *   处理音频焦点 (`AudioManager.OnAudioFocusChangeListener`)。
    *   响应耳机插拔、蓝牙连接等系统事件。
    *   管理媒体通知 (`Notification`)，允许通过通知栏控制播放。**需要 `FOREGROUND_SERVICE` 权限 (Manifest声明)，并在Android 13+运行时请求 `POST_NOTIFICATIONS` 权限。**
    *   提供接口供 `Activity`/`Fragment` (通常通过`ViewModel`) 绑定或发送命令，并观察其状态。
*   **交互**: 各UI组件（`PlayerFragment`, `DrivingModeFragment`, `MusicLibraryFragment`中的列表项点击等）不直接操作播放器，而是与此服务通信。

## 三、 核心开发语言与规范 (遵循用户指示)

1.  **开发语言**: **严格使用纯 Java 语言**。
2.  **代码风格与注释**:
    *   严格遵循标准的 Java 编码规范和 Android 开发最佳实践。
    *   代码易于理解、清晰明了。
    *   **JavaDoc 注释**: 为所有主要类、公共方法、复杂逻辑块添加清晰、准确的 JavaDoc 注释。
    *   **XML 视图 ID**: 清晰、有意义，遵循统一命名约定 (e.g., `button_submit`, `textview_username`)。

## 四、 UI/UX 设计与资源 (遵循用户指示)

1.  **原型参考**: 主要参考项目提供的 `car.html` **横屏原型** 与 **原型图**。
    *   **全局UI**: 所有页面均不显示顶部标题栏 (ActionBar/Toolbar)。
    *   欢迎页和登录页 UI **不应**重新开发或过度修改，仅做集成。
2.  **资源文件**: 图标、图片等美观，并做好不同屏幕密度的适配。
3.  **全屏模式**: 应用内所有界面均需取消系统底部导航栏，实现真正的全屏显示。
4.  **用户体验**:
    *   适当使用加载动画和过渡效果。
    *   **侧边导航栏自动隐藏/滑出**：提升沉浸感，具体交互方式待细化。
    *   确保响应式布局和良好体验。

## 五、 特定功能要求：驾驶模式 (遵循用户指示)

1.  **UI 设计**: 简洁、易于操作。按钮尺寸显著增大，界面元素对比度高。
2.  **交互方式**: **语音交互** 作为主要操作方式。
3.  **快捷操作**: 提供播放控制、快速访问播放列表、音量调节等。

## 六、 API 集成与数据处理 (遵循用户指示)

1.  **API 接口**: 所有后端 API 的调用方法和预期数据结构，应严格参考 `api.txt` 文档。
    *   **开发前置检查**: 在开发依赖API的功能模块前（如我的音乐库、音乐探索、用户中心的部分功能），**必须首先确认`api.txt`中是否存在对应的接口。若接口缺失，则暂停该功能的开发，并明确向用户报告所缺少的具体接口和功能点。**
2.  **语音技术集成**: 参考 `baidu.txt` (若提供) 及项目中已有逻辑。
3.  **动态数据优先与占位符策略**:
    *   **优先动态数据**: 歌曲信息、用户信息、推荐内容、封面等**必须**优先尝试通过内部状态、缓存或API获取真实数据。
    *   **占位符的审慎使用**: 仅在真实数据确实无法获取（例如，API 调用失败且无可用缓存，或特定数据字段确认为空且无合适的默认值）的极端情况下，才可考虑使用统一的、明确标记为临时的占位符。
    *   **占位符注释**: 使用占位符时，**必须**在代码注释中明确指出原因，并提示后续处理。

## 七、 性能与健壮性 (遵循用户指示)

1.  **内存管理**: 务必关注内存泄漏问题，确保在组件销毁或不再需要时，及时释放不再使用的资源。
2.  **错误处理**:
    *   实现统一、规范的错误处理机制。
    *   对于用户操作可能发生的失败（如网络连接错误、API 返回错误码、数据解析异常等），必须向用户提供友好且显眼的提示信息。

## 八、 AI 编码与反馈机制 (遵循用户指示)

1.  **文件完整性AI提示**: 完成模块或功能编码后，AI 应主动提示检查是否所有必要文件都已生成或更新。
2.  **代码交付标准**:
    *   **完整代码**: 避免使用省略号 (`...`) 或伪代码，除非明确要求。
    *   **解释说明**: 对复杂逻辑、不常见API、关键决策，AI应主动提供简要解释或注释。
3.  **结构化反馈 (生成到独立的 .md 文件)**: 交付主要代码块或模块后，AI 应提供一个总结性的 Markdown 文件，包含：
    *   整体功能和作用
    *   UI 组件结构 (如适用)
    *   状态管理和数据流 (特别是与统一播放服务的交互)
    *   用户交互和事件处理
    *   API 交互 (如适用), 包括对`api.txt`的依赖情况和缺失接口的说明。
    *   关键依赖或技术栈
    *   **呈现要求**: 反馈清晰、结构化，**侧重于架构和关键点的把握，避免详细的代码实现细节或逐行解释。**

## 九、 其他关键指示 (遵循用户指示)

*   **响应语言**: AI 必须始终使用 **中文** 进行所有交流、代码注释。
*   **文件完整性审查**: 开发和AI都应仔细对照"核心组件/文件"列表及实现细节，审查是否遗漏。
*   **语音模式功能**: 现有语音模式相关代码 (若已提供) 应尽量避免修改，专注于平滑集成。
*   **冗余文件清理**: AI可主动识别或在指示下删除不再使用的文件。
*   **功能开发范围与流程遵循**:
    *   **严格遵循需求**: 必须严格遵循用户提供的功能需求、设计规格、预定业务逻辑。
    *   **禁止超范围开发**: 严禁实现任何未经确认的功能。若有更好建议，应先提出并等待用户确认。

## 十、 开发流程与协作规范

本章节明确AI与开发者在项目中的角色分工，以及各开发阶段的具体要求和协作方式，旨在提升开发效率和代码质量。

### 10.1 角色定义

*   **AI (Roo)**:
    *   **代码生成与修改**: 根据用户需求、本指南及其他相关文档（如 `api.txt`）生成、修改和优化代码。
    *   **文档编写与维护**: 负责编写和更新开发相关文档，特别是本《核心流程与开发指南》以及阶段性总结报告。
    *   **技术分析与建议**: 提供技术方案分析、潜在问题预警、代码优化建议。
    *   **辅助调试**: 根据用户反馈，协助定位和修复问题。
    *   **任务执行**: 严格按照用户指令和预设流程执行开发任务，确保每个阶段的完整性。

*   **开发者 (用户)**:
    *   **需求定义与澄清**: 提出明确、完整的开发需求，并在AI提问时及时澄清模糊点。
    *   **代码审核与决策**: 审核AI生成的代码和文档，对技术方案和实现路径进行最终决策。
    *   **编译、测试与验证**: 负责代码的编译、部署、运行测试，并验证功能是否符合预期。
    *   **反馈提供**: 及时、准确地向AI提供测试结果、错误信息、修改意见和下一步指示。
    *   **项目管理**: 整体把控项目进度和方向。

### 10.2 开发阶段与要求

#### 10.2.1 需求理解与规划
*   **AI**:
    *   仔细阅读并充分理解用户下达的每一个任务指令。
    *   主动查阅并关联所有相关文档，特别是本指南、`api.txt`、`开发者指南.md`等。
    *   若需求不明确或存在冲突，应主动向开发者提问以获取澄清。
*   **开发者**:
    *   提供清晰、具体、无歧义的需求描述。
    *   确保所有必要的上下文信息（如关联模块、预期行为）已提供给AI。

#### 10.2.2 编码实现
*   **AI**:
    *   严格遵循本指南中定义的所有规范，包括但不限于：Android Automotive特性、MVVM架构、纯Java语言、UI/UX要求（无标题栏、全屏、横屏优化）、API接口规范（严格参考 `api.txt`）、代码风格与注释标准。
    *   **优先重构**: 对于已有的相似功能或旧代码，应优先考虑在其基础上进行重构和优化，避免不必要的文件和代码冗余。
    *   **新建与删除**: 若确实需要新建文件，应确保其必要性，并在旧有可替代文件不再使用时，主动提示或在用户同意后进行删除。
    *   **API依赖处理**: 严格按照 `api.txt` 实现API调用。若 `api.txt` 缺失所需接口，必须暂停对应功能的开发，并立即向开发者报告所缺接口及受影响的功能点。
*   **开发者**:
    *   在AI进行编码前，确认相关设计和技术选型。
    *   对AI提出的关于重构、新建或删除文件的建议进行评估和决策。

#### 10.2.3 逐步验证
*   **AI**:
    *   完成每个主要模块、重要功能点或用户指定的阶段性任务后，应主动告知开发者当前阶段已完成。
    *   清晰总结本次交付实现的核心功能和主要变更点。
*   **开发者**:
    *   在AI完成阶段性代码交付后，**必须进行编译和启动测试**，确保应用可正常运行。
    *   **必须对新实现的功能进行实际操作验证**，检查其是否符合预期。
    *   将编译结果、运行状态（成功/失败及错误信息）、功能验证结果及时反馈给AI。

#### 10.2.4 结构化反馈 (阶段性总结)
*   **AI**:
    *   在完成一个主要模块或一个较大的阶段性任务后，除了即时反馈，还需提供一个结构化的总结报告（通常是更新本文档的相应章节或生成独立的Markdown摘要，并**整合进《开发者指南.md》**）。
    *   总结报告应至少包含：
        1.  **已实现功能**: 清单式列出本次交付完成的具体功能点及其范围。
        2.  **代码结构变动**: 描述主要新增或修改的类、文件及其组织关系。
        3.  **核心交互逻辑**: 简述关键的用户交互流程和内部数据流转，特别是与统一播放服务 `UnifiedPlaybackService` 的交互。
        4.  **API 依赖**: 明确指出本次实现所依赖的 `api.txt` 中的接口。**重点高亮任何缺失、不符或调用失败的API接口及其对功能造成的影响。**
        5.  **关键技术点/决策**: 说明在实现过程中采用的关键技术、算法或重要的设计决策。
        6.  **待办或已知问题**: 列出尚未完成的子任务、已知缺陷或潜在风险。
        7.  **优化建议 (可选)**: 提出针对当前实现或后续开发的可行优化建议。
*   **开发者**:
    *   仔细审阅AI提供的结构化反馈。
    *   针对反馈中的内容进行确认，对不清晰或有异议的部分与AI进一步沟通。

#### 10.2.5 文档同步
*   **AI**:
    *   **核心要求**: **每一次代码修改或功能实现完成后，都必须将相关的更新（如新功能描述、API使用变更、架构调整等）同步整合到《开发者指南.md》中。**
    *   在开始编写新的代码或模块前，**必须重新审读最新的《开发者指南.md》**，确保基于最新的共识进行开发。
*   **开发者**:
    *   监督AI对《开发者指南.md》的更新情况，确保其内容的准确性和实时性。
    *   在必要时，手动修正或补充指南内容。

### 10.3 沟通与协作
*   **AI**:
    *   **主动报告**: 在开发过程中遇到任何问题（如需求冲突、技术障碍、API不符预期、`api.txt` 缺失关键接口等）时，应立即暂停相关任务并主动向开发者报告，说明问题详情和可能的影响。
    *   **清晰提问**: 当需要开发者提供决策或信息时，提问应具体、清晰，并提供必要的上下文和选项（如果可能）。
    *   **持续反馈**: 即使任务正在顺利进行，也应在关键节点向开发者同步进度。
*   **开发者**:
    *   **及时响应**: 尽快回复AI的提问和报告，提供明确的指示、决策或所需信息，避免阻塞开发进程。
    *   **明确指示**: 给AI下达任务时，指令应清晰、完整，避免模糊不清。
    *   **耐心指导**: 在AI理解出现偏差或需要调整方向时，给予清晰的指导。

### 10.4 任务持续性
*   **AI**:
    *   如果用户给出的任务是分阶段的，AI必须完整地执行所有阶段，持续进行开发，直到整个任务真正完成。
    *   在每个阶段完成后，应主动总结并询问用户是否继续下一阶段，或根据预设流程自动进入下一阶段。
*   **开发者**:
    *   对AI的阶段性成果进行确认，并明确指示是否进入下一阶段的开发。
